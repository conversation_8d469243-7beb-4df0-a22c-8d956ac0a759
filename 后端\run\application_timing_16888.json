[{"name": "Process Start", "start": 1755792610306, "end": 1755792610704, "duration": 398, "pid": 16888, "index": 0}, {"name": "Application Start", "start": 1755792610705, "end": 1755792611132, "duration": 427, "pid": 16888, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1755792610713, "end": 1755792610723, "duration": 10, "pid": 16888, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1755792610723, "end": 1755792610735, "duration": 12, "pid": 16888, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1755792610723, "end": 1755792610724, "duration": 1, "pid": 16888, "index": 4}, {"name": "Require(1) node_modules/egg-session/config/config.default.js", "start": 1755792610725, "end": 1755792610726, "duration": 1, "pid": 16888, "index": 5}, {"name": "Require(2) node_modules/egg-security/config/config.default.js", "start": 1755792610726, "end": 1755792610726, "duration": 0, "pid": 16888, "index": 6}, {"name": "Require(3) node_modules/egg-jsonp/config/config.default.js", "start": 1755792610726, "end": 1755792610727, "duration": 1, "pid": 16888, "index": 7}, {"name": "Require(4) node_modules/egg-onerror/config/config.default.js", "start": 1755792610727, "end": 1755792610727, "duration": 0, "pid": 16888, "index": 8}, {"name": "Require(5) node_modules/egg-i18n/config/config.default.js", "start": 1755792610727, "end": 1755792610727, "duration": 0, "pid": 16888, "index": 9}, {"name": "Require(6) node_modules/egg-watcher/config/config.default.js", "start": 1755792610728, "end": 1755792610728, "duration": 0, "pid": 16888, "index": 10}, {"name": "Require(7) node_modules/egg-schedule/config/config.default.js", "start": 1755792610728, "end": 1755792610728, "duration": 0, "pid": 16888, "index": 11}, {"name": "Require(8) node_modules/egg-multipart/config/config.default.js", "start": 1755792610729, "end": 1755792610729, "duration": 0, "pid": 16888, "index": 12}, {"name": "Require(9) node_modules/egg-development/config/config.default.js", "start": 1755792610729, "end": 1755792610729, "duration": 0, "pid": 16888, "index": 13}, {"name": "Require(10) node_modules/egg-logrotator/config/config.default.js", "start": 1755792610729, "end": 1755792610730, "duration": 1, "pid": 16888, "index": 14}, {"name": "Require(11) node_modules/egg-static/config/config.default.js", "start": 1755792610730, "end": 1755792610730, "duration": 0, "pid": 16888, "index": 15}, {"name": "Require(12) node_modules/egg-view/config/config.default.js", "start": 1755792610730, "end": 1755792610730, "duration": 0, "pid": 16888, "index": 16}, {"name": "Require(13) node_modules/egg-sequelize/config/config.default.js", "start": 1755792610731, "end": 1755792610731, "duration": 0, "pid": 16888, "index": 17}, {"name": "Require(14) node_modules/egg-cors/config/config.default.js", "start": 1755792610731, "end": 1755792610731, "duration": 0, "pid": 16888, "index": 18}, {"name": "Require(15) node_modules/egg-view-nunjucks/config/config.default.js", "start": 1755792610731, "end": 1755792610731, "duration": 0, "pid": 16888, "index": 19}, {"name": "Require(16) node_modules/egg/config/config.default.js", "start": 1755792610732, "end": 1755792610732, "duration": 0, "pid": 16888, "index": 20}, {"name": "Require(17) config/config.default.js", "start": 1755792610733, "end": 1755792610733, "duration": 0, "pid": 16888, "index": 21}, {"name": "Require(18) node_modules/egg-security/config/config.local.js", "start": 1755792610733, "end": 1755792610733, "duration": 0, "pid": 16888, "index": 22}, {"name": "Require(19) node_modules/egg-watcher/config/config.local.js", "start": 1755792610734, "end": 1755792610734, "duration": 0, "pid": 16888, "index": 23}, {"name": "Require(20) node_modules/egg-view/config/config.local.js", "start": 1755792610734, "end": 1755792610734, "duration": 0, "pid": 16888, "index": 24}, {"name": "Require(21) node_modules/egg-view-nunjucks/config/config.local.js", "start": 1755792610735, "end": 1755792610735, "duration": 0, "pid": 16888, "index": 25}, {"name": "Require(22) node_modules/egg/config/config.local.js", "start": 1755792610735, "end": 1755792610735, "duration": 0, "pid": 16888, "index": 26}, {"name": "Load extend/application.js", "start": 1755792610736, "end": 1755792610758, "duration": 22, "pid": 16888, "index": 27}, {"name": "Require(23) node_modules/egg-session/app/extend/application.js", "start": 1755792610736, "end": 1755792610736, "duration": 0, "pid": 16888, "index": 28}, {"name": "Require(24) node_modules/egg-security/app/extend/application.js", "start": 1755792610737, "end": 1755792610737, "duration": 0, "pid": 16888, "index": 29}, {"name": "Require(25) node_modules/egg-jsonp/app/extend/application.js", "start": 1755792610737, "end": 1755792610739, "duration": 2, "pid": 16888, "index": 30}, {"name": "Require(26) node_modules/egg-schedule/app/extend/application.js", "start": 1755792610739, "end": 1755792610741, "duration": 2, "pid": 16888, "index": 31}, {"name": "Require(27) node_modules/egg-logrotator/app/extend/application.js", "start": 1755792610741, "end": 1755792610742, "duration": 1, "pid": 16888, "index": 32}, {"name": "Require(28) node_modules/egg-view/app/extend/application.js", "start": 1755792610742, "end": 1755792610743, "duration": 1, "pid": 16888, "index": 33}, {"name": "Require(29) node_modules/egg-view-nunjucks/app/extend/application.js", "start": 1755792610743, "end": 1755792610756, "duration": 13, "pid": 16888, "index": 34}, {"name": "Load extend/request.js", "start": 1755792610758, "end": 1755792610760, "duration": 2, "pid": 16888, "index": 35}, {"name": "Require(30) node_modules/egg/app/extend/request.js", "start": 1755792610759, "end": 1755792610759, "duration": 0, "pid": 16888, "index": 36}, {"name": "Load extend/response.js", "start": 1755792610760, "end": 1755792610763, "duration": 3, "pid": 16888, "index": 37}, {"name": "Require(31) node_modules/egg/app/extend/response.js", "start": 1755792610762, "end": 1755792610762, "duration": 0, "pid": 16888, "index": 38}, {"name": "Load extend/context.js", "start": 1755792610763, "end": 1755792610781, "duration": 18, "pid": 16888, "index": 39}, {"name": "Require(32) node_modules/egg-security/app/extend/context.js", "start": 1755792610764, "end": 1755792610768, "duration": 4, "pid": 16888, "index": 40}, {"name": "Require(33) node_modules/egg-jsonp/app/extend/context.js", "start": 1755792610769, "end": 1755792610769, "duration": 0, "pid": 16888, "index": 41}, {"name": "Require(34) node_modules/egg-i18n/app/extend/context.js", "start": 1755792610769, "end": 1755792610770, "duration": 1, "pid": 16888, "index": 42}, {"name": "Require(35) node_modules/egg-multipart/app/extend/context.js", "start": 1755792610770, "end": 1755792610778, "duration": 8, "pid": 16888, "index": 43}, {"name": "Require(36) node_modules/egg-view/app/extend/context.js", "start": 1755792610779, "end": 1755792610779, "duration": 0, "pid": 16888, "index": 44}, {"name": "Require(37) node_modules/egg/app/extend/context.js", "start": 1755792610780, "end": 1755792610780, "duration": 0, "pid": 16888, "index": 45}, {"name": "Load extend/helper.js", "start": 1755792610781, "end": 1755792610792, "duration": 11, "pid": 16888, "index": 46}, {"name": "Require(38) node_modules/egg-security/app/extend/helper.js", "start": 1755792610782, "end": 1755792610789, "duration": 7, "pid": 16888, "index": 47}, {"name": "Require(39) node_modules/egg/app/extend/helper.js", "start": 1755792610790, "end": 1755792610791, "duration": 1, "pid": 16888, "index": 48}, {"name": "Load app.js", "start": 1755792610792, "end": 1755792610809, "duration": 17, "pid": 16888, "index": 49}, {"name": "Require(40) node_modules/egg-session/app.js", "start": 1755792610792, "end": 1755792610792, "duration": 0, "pid": 16888, "index": 50}, {"name": "Require(41) node_modules/egg-security/app.js", "start": 1755792610792, "end": 1755792610793, "duration": 1, "pid": 16888, "index": 51}, {"name": "Require(42) node_modules/egg-onerror/app.js", "start": 1755792610793, "end": 1755792610797, "duration": 4, "pid": 16888, "index": 52}, {"name": "Require(43) node_modules/egg-i18n/app.js", "start": 1755792610797, "end": 1755792610802, "duration": 5, "pid": 16888, "index": 53}, {"name": "Require(44) node_modules/egg-watcher/app.js", "start": 1755792610802, "end": 1755792610805, "duration": 3, "pid": 16888, "index": 54}, {"name": "Require(45) node_modules/egg-schedule/app.js", "start": 1755792610805, "end": 1755792610805, "duration": 0, "pid": 16888, "index": 55}, {"name": "Require(46) node_modules/egg-multipart/app.js", "start": 1755792610806, "end": 1755792610806, "duration": 0, "pid": 16888, "index": 56}, {"name": "Require(47) node_modules/egg-development/app.js", "start": 1755792610806, "end": 1755792610807, "duration": 1, "pid": 16888, "index": 57}, {"name": "Require(48) node_modules/egg-logrotator/app.js", "start": 1755792610807, "end": 1755792610807, "duration": 0, "pid": 16888, "index": 58}, {"name": "Require(49) node_modules/egg-static/app.js", "start": 1755792610807, "end": 1755792610807, "duration": 0, "pid": 16888, "index": 59}, {"name": "Require(50) node_modules/egg-sequelize/app.js", "start": 1755792610807, "end": 1755792610807, "duration": 0, "pid": 16888, "index": 60}, {"name": "Require(51) node_modules/egg-cors/app.js", "start": 1755792610808, "end": 1755792610808, "duration": 0, "pid": 16888, "index": 61}, {"name": "Require(52) node_modules/egg-view-nunjucks/app.js", "start": 1755792610808, "end": 1755792610808, "duration": 0, "pid": 16888, "index": 62}, {"name": "Require(53) app.js", "start": 1755792610808, "end": 1755792610808, "duration": 0, "pid": 16888, "index": 63}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1755792610816, "end": 1755792611116, "duration": 300, "pid": 16888, "index": 64}, {"name": "Load \"Symbol(model)\" to Application", "start": 1755792611031, "end": 1755792611041, "duration": 10, "pid": 16888, "index": 65}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1755792611042, "end": 1755792611131, "duration": 89, "pid": 16888, "index": 66}, {"name": "Did Load in app.js:didLoad", "start": 1755792611043, "end": 1755792611110, "duration": 67, "pid": 16888, "index": 67}, {"name": "Load Service", "start": 1755792611043, "end": 1755792611047, "duration": 4, "pid": 16888, "index": 68}, {"name": "Load \"service\" to Context", "start": 1755792611043, "end": 1755792611047, "duration": 4, "pid": 16888, "index": 69}, {"name": "Load Middleware", "start": 1755792611047, "end": 1755792611093, "duration": 46, "pid": 16888, "index": 70}, {"name": "Load \"middlewares\" to Application", "start": 1755792611047, "end": 1755792611088, "duration": 41, "pid": 16888, "index": 71}, {"name": "Load Controller", "start": 1755792611093, "end": 1755792611097, "duration": 4, "pid": 16888, "index": 72}, {"name": "Load \"controller\" to Application", "start": 1755792611093, "end": 1755792611097, "duration": 4, "pid": 16888, "index": 73}, {"name": "Load Router", "start": 1755792611097, "end": 1755792611099, "duration": 2, "pid": 16888, "index": 74}, {"name": "Require(54) app/router.js", "start": 1755792611097, "end": 1755792611097, "duration": 0, "pid": 16888, "index": 75}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1755792611097, "end": 1755792611110, "duration": 13, "pid": 16888, "index": 76}, {"name": "Will Ready in app.js:will<PERSON><PERSON>y", "start": 1755792611132, "end": 1755792611132, "duration": 0, "pid": 16888, "index": 77}]
2025-08-22 00:00:07,788 INFO 30224 [-/127.0.0.1/-/5ms POST /paths/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `paths` AS `paths` WHERE `paths`.`code` = 'PATH_MDSM0MKR_0PEGO' AND `paths`.`uid` = 1;
2025-08-22 00:00:07,788 INFO 30224 [-/127.0.0.1/-/5ms POST /paths/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `code`, `title`, `jsons`, `time`, `uid` FROM `paths` AS `paths` WHERE `paths`.`code` = 'PATH_MDSM0MKR_0PEGO' AND `paths`.`uid` = 1 ORDER BY `paths`.`id` DESC LIMIT 0, 1;
2025-08-22 00:00:07,819 INFO 30224 [-/127.0.0.1/-/2ms POST /foods/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`pid` = 1;
2025-08-22 00:00:07,821 INFO 30224 [-/127.0.0.1/-/4ms POST /foods/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `foods` AS `foods` WHERE `foods`.`pid` = 1 ORDER BY `foods`.`id` DESC LIMIT 0, 999;
2025-08-22 00:00:07,843 INFO 30224 [-/127.0.0.1/-/2ms POST /hotels/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `hotels` AS `hotels` WHERE `hotels`.`pid` = 1 ORDER BY `hotels`.`id` DESC LIMIT 0, 999;
2025-08-22 00:00:07,845 INFO 30224 [-/127.0.0.1/-/4ms POST /hotels/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `hotels` AS `hotels` WHERE `hotels`.`pid` = 1;
2025-08-22 00:01:43,757 INFO 30224 [-/127.0.0.1/-/8ms POST /paths/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `paths` AS `paths` WHERE `paths`.`code` = 'PATH_MDSM0MKR_0PEGO' AND `paths`.`uid` = 1;
2025-08-22 00:01:43,757 INFO 30224 [-/127.0.0.1/-/8ms POST /paths/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `code`, `title`, `jsons`, `time`, `uid` FROM `paths` AS `paths` WHERE `paths`.`code` = 'PATH_MDSM0MKR_0PEGO' AND `paths`.`uid` = 1 ORDER BY `paths`.`id` DESC LIMIT 0, 1;
2025-08-22 00:01:44,375 INFO 30224 [-/127.0.0.1/-/1ms POST /paths/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `paths` AS `paths` WHERE `paths`.`code` = 'PATH_MDSM0MKR_0PEGO' AND `paths`.`uid` = 1;
2025-08-22 00:01:44,376 INFO 30224 [-/127.0.0.1/-/2ms POST /paths/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `code`, `title`, `jsons`, `time`, `uid` FROM `paths` AS `paths` WHERE `paths`.`code` = 'PATH_MDSM0MKR_0PEGO' AND `paths`.`uid` = 1 ORDER BY `paths`.`id` DESC LIMIT 0, 1;
2025-08-22 00:01:44,398 INFO 30224 [-/127.0.0.1/-/4ms POST /foods/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`pid` = 1;
2025-08-22 00:01:44,399 INFO 30224 [-/127.0.0.1/-/5ms POST /foods/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `foods` AS `foods` WHERE `foods`.`pid` = 1 ORDER BY `foods`.`id` DESC LIMIT 0, 999;
2025-08-22 00:01:44,436 INFO 30224 [-/127.0.0.1/-/4ms POST /hotels/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `hotels` AS `hotels` WHERE `hotels`.`pid` = 1;
2025-08-22 00:01:44,437 INFO 30224 [-/127.0.0.1/-/5ms POST /hotels/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `hotels` AS `hotels` WHERE `hotels`.`pid` = 1 ORDER BY `hotels`.`id` DESC LIMIT 0, 999;
2025-08-22 00:06:05,573 INFO 30224 [-/127.0.0.1/-/5ms POST /paths/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `paths` AS `paths` WHERE `paths`.`code` = 'PATH_MDSM0MKR_0PEGO' AND `paths`.`uid` = 1;
2025-08-22 00:06:05,574 INFO 30224 [-/127.0.0.1/-/6ms POST /paths/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `code`, `title`, `jsons`, `time`, `uid` FROM `paths` AS `paths` WHERE `paths`.`code` = 'PATH_MDSM0MKR_0PEGO' AND `paths`.`uid` = 1 ORDER BY `paths`.`id` DESC LIMIT 0, 1;
2025-08-22 00:06:05,603 INFO 30224 [-/127.0.0.1/-/2ms POST /foods/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`pid` = 1;
2025-08-22 00:06:05,603 INFO 30224 [-/127.0.0.1/-/2ms POST /foods/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `foods` AS `foods` WHERE `foods`.`pid` = 1 ORDER BY `foods`.`id` DESC LIMIT 0, 999;
2025-08-22 00:06:05,626 INFO 30224 [-/127.0.0.1/-/1ms POST /hotels/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `hotels` AS `hotels` WHERE `hotels`.`pid` = 1;
2025-08-22 00:06:05,627 INFO 30224 [-/127.0.0.1/-/2ms POST /hotels/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `hotels` AS `hotels` WHERE `hotels`.`pid` = 1 ORDER BY `hotels`.`id` DESC LIMIT 0, 999;
2025-08-22 00:07:21,887 INFO 30224 [-/127.0.0.1/-/5ms POST /paths/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `code`, `title`, `jsons`, `time`, `uid` FROM `paths` AS `paths` WHERE `paths`.`code` = 'PATH_MDSM0MKR_0PEGO' AND `paths`.`uid` = 1 ORDER BY `paths`.`id` DESC LIMIT 0, 1;
2025-08-22 00:07:21,888 INFO 30224 [-/127.0.0.1/-/6ms POST /paths/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `paths` AS `paths` WHERE `paths`.`code` = 'PATH_MDSM0MKR_0PEGO' AND `paths`.`uid` = 1;
2025-08-22 00:07:21,939 INFO 30224 [-/127.0.0.1/-/2ms POST /foods/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`pid` = 1;
2025-08-22 00:07:21,939 INFO 30224 [-/127.0.0.1/-/2ms POST /foods/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `foods` AS `foods` WHERE `foods`.`pid` = 1 ORDER BY `foods`.`id` DESC LIMIT 0, 999;
2025-08-22 00:07:21,962 INFO 30224 [-/127.0.0.1/-/5ms POST /hotels/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `hotels` AS `hotels` WHERE `hotels`.`pid` = 1;
2025-08-22 00:07:21,962 INFO 30224 [-/127.0.0.1/-/5ms POST /hotels/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `hotels` AS `hotels` WHERE `hotels`.`pid` = 1 ORDER BY `hotels`.`id` DESC LIMIT 0, 999;
2025-08-22 00:08:30,346 INFO 30224 [-/127.0.0.1/-/6ms POST /paths/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `code`, `title`, `jsons`, `time`, `uid` FROM `paths` AS `paths` WHERE `paths`.`code` = 'PATH_MDSM0MKR_0PEGO' AND `paths`.`uid` = 1 ORDER BY `paths`.`id` DESC LIMIT 0, 1;
2025-08-22 00:08:30,347 INFO 30224 [-/127.0.0.1/-/7ms POST /paths/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `paths` AS `paths` WHERE `paths`.`code` = 'PATH_MDSM0MKR_0PEGO' AND `paths`.`uid` = 1;
2025-08-22 00:08:30,395 INFO 30224 [-/127.0.0.1/-/2ms POST /foods/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`pid` = 1;
2025-08-22 00:08:30,395 INFO 30224 [-/127.0.0.1/-/2ms POST /foods/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `foods` AS `foods` WHERE `foods`.`pid` = 1 ORDER BY `foods`.`id` DESC LIMIT 0, 999;
2025-08-22 00:08:30,426 INFO 30224 [-/127.0.0.1/-/5ms POST /hotels/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `hotels` AS `hotels` WHERE `hotels`.`pid` = 1;
2025-08-22 00:08:30,427 INFO 30224 [-/127.0.0.1/-/6ms POST /hotels/list] [egg-sequelize](5ms) Executed (default): SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `hotels` AS `hotels` WHERE `hotels`.`pid` = 1 ORDER BY `hotels`.`id` DESC LIMIT 0, 999;
2025-08-22 00:10:11,293 INFO 18872 [egg-sequelize](0ms) Executed (default): SELECT 1+1 AS result

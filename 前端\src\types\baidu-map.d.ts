// 百度地图 BMapGL API 类型声明
declare global {
  const BMapGL: {
    Map: new (container: string | HTMLElement, opts?: any) => any;
    Point: new (lng: number, lat: number) => any;
    Marker: new (point: any, opts?: any) => any;
    Label: new (content: string, opts?: any) => any;
    Size: new (width: number, height: number) => any;
    ScaleControl: new (opts?: any) => any;
    ZoomControl: new (opts?: any) => any;
    CityListControl: new (opts?: any) => any;
    Geolocation: new (opts?: any) => any;
    Geocoder: new (opts?: any) => any;
    LocalSearch: new (map: any, opts?: any) => any;
    Polyline: new (points: any[], opts?: any) => any;
    DrivingRoute: new (map: any, opts?: any) => any;
    DrivingRouteLine: new (map: any, opts?: any) => any;
  };

  const BMapGLLib: {
    TrackAnimation: new (map: any, polyline: any, opts?: any) => any;
  };

  const BMAP_STATUS_SUCCESS: number;
}

export {};

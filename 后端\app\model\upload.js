'use strict';

module.exports = app => {
  const { STRING, INTEGER, DATE, TEXT } = app.Sequelize;

  const Upload = app.model.define('upload', {
    id: {
      type: INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '文件ID'
    },
    originalName: {
      type: STRING(255),
      allowNull: false,
      comment: '原始文件名'
    },
    fileName: {
      type: STRING(255),
      allowNull: false,
      unique: true,
      comment: '存储文件名'
    },
    filePath: {
      type: STRING(500),
      allowNull: false,
      comment: '文件路径'
    },
    fileSize: {
      type: INTEGER,
      allowNull: false,
      comment: '文件大小(字节)'
    },
    mimeType: {
      type: STRING(100),
      allowNull: true,
      comment: '文件MIME类型'
    },
    fileType: {
      type: STRING(50),
      allowNull: true,
      comment: '文件类型(图片、文档、视频等)'
    },
    description: {
      type: TEXT,
      allowNull: true,
      comment: '文件描述'
    },
    tags: {
      type: STRING(500),
      allowNull: true,
      comment: '文件标签，逗号分隔'
    },
    uploadUserId: {
      type: INTEGER,
      allowNull: true,
      comment: '上传用户ID'
    },
    uploadTime: {
      type: DATE,
      allowNull: false,
      defaultValue: app.Sequelize.NOW,
      comment: '上传时间'
    },
    downloadCount: {
      type: INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '下载次数'
    },
    isPublic: {
      type: INTEGER,
      allowNull: false,
      defaultValue: 1,
      comment: '是否公开(1:公开 0:私有)'
    },
    status: {
      type: INTEGER,
      allowNull: false,
      defaultValue: 1,
      comment: '文件状态(1:正常 0:已删除)'
    }
  }, {
    tableName: 'uploads',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['fileName']
      },
      {
        fields: ['uploadUserId']
      },
      {
        fields: ['fileType']
      },
      {
        fields: ['uploadTime']
      }
    ]
  });

  // 关联关系
  Upload.associate = function() {
    // 与用户表的关联
    app.model.Upload.belongsTo(app.model.User, {
      foreignKey: 'uploadUserId',
      as: 'uploadUser'
    });
  };

  return Upload;
}; 
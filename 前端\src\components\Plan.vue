<template>
  <div>
    <!-- 标点管理按钮 -->
    <van-button @click="showPlanPopupOpen" type="warning" icon="guide-o" class="w-full">
      路径
    </van-button>

    <!-- 路径规划列表 -->
    <van-popup v-model:show="showPlanPopup" position="bottom" :style="{ height: '80%' }">
      <van-nav-bar :title="'路径规划 ' + plansList.length" @click-right="showCreateDialogOpen">
        <template #right>
          <van-button type="primary" size="small">新建路径规划</van-button>
        </template>
      </van-nav-bar>
      <div class="plans-list">
        <van-empty v-if="!plansList.length" description="暂无路径规划" />
        <div v-else class="plan-items">
          <div v-for="plan in plansList" :key="plan.id" class="plan-item" @click="viewPlanDetail(plan)">
            <div class="plan-header">
              <h3 class="plan-title">{{ plan.title }}</h3>
              <div class="plan-actions flex items-center">
                <div class="pr-4 flex items-center">
                  <van-button type="success" size="mini" @click.stop="planLint(plan)">
                    线路
                  </van-button>
                </div>
                <van-button type="primary" size="mini" @click.stop="editPlan(plan)">
                  编辑
                </van-button>
                <van-button type="danger" size="mini" @click.stop="deletePlan(plan.id!)">
                  删除
                </van-button>
              </div>
            </div>
            <div class="plan-info">
              <span class="plan-code">编号: {{ plan.code }}</span>

              <div class="flex items-center plan-actions" v-if="plan.id">
                <div class="pr-2.5 flex items-center">
                  <van-button color="#666" size="mini" @click.stop="pid = plan.id; getHotelsList()">
                    住宿
                  </van-button>
                </div>
                <van-button color="#FF9800" size="mini" @click.stop="pid = plan.id; getFoodsList()">
                  美食
                </van-button>
              </div>
            </div>
            <div class="plan-locations flex items-center justify-between">
              <span class="locations-count">
                包含 {{ getPlanLocationsCount(plan.jsons) }} 个地点
              </span>
              <span class="plan-time">{{ plan.time }}</span>
            </div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 创建/编辑路径规划弹窗 -->
    <van-dialog v-model:show="showCreateDialog" :title="editingPlan ? '编辑路径规划' : '新建路径规划'" show-cancel-button
      @confirm="savePlan" @cancel="resetForm" :close-on-click-overlay="false" class="plan-dialog">
      <div class="form-content">
        <!-- 路径名称 -->
        <van-field v-model="formData.title" label="路径名称" placeholder="请输入路径名称" required />

        <!-- 地点选择区域 -->
        <div class="locations-section">
          <div class="section-title">选择地点并排序</div>

          <!-- 可选地点列表 -->
          <div class="available-locations">
            <div class="subtitle">可选地点:</div>
            <div class="location-chips">
              <van-tag v-for="location in availableLocations" :key="location.id"
                :type="selectedLocationIds.includes(location.id) ? 'primary' : 'default'"
                @click="toggleLocation(location)" class="location-chip">
                {{ location.name }}
              </van-tag>
            </div>
          </div>

          <!-- 已选地点排序 -->
          <div class="selected-locations" v-if="selectedLocations.length">
            <div class="subtitle">路径顺序 (点击按钮调整顺序):</div>
            <div class="sorted-locations">
              <div v-for="(location, index) in selectedLocations" :key="location.id" class="sorted-location-item">
                <span class="location-order">{{ index + 1 }}</span>
                <span class="location-name">{{ location.name }}</span>
                <div class="location-actions">
                  <van-button v-if="index > 0" type="primary" size="mini" icon="arrow-up" @click="moveLocationUp(index)"
                    class="move-btn" />
                  <van-button v-if="index < selectedLocations.length - 1" type="primary" size="mini" icon="arrow-down"
                    @click="moveLocationDown(index)" class="move-btn" />
                  <van-button type="danger" size="mini" icon="cross" @click="removeLocation(index)"
                    class="remove-btn" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </van-dialog>

    <!-- 路径详情弹窗 -->
    <van-dialog v-model:show="showDetailDialog" :title="currentPlan?.title || '路径详情'" show-cancel-button
      confirm-button-text="关闭" :show-confirm-button="false" cancel-button-text="关闭" class="detail-dialog">
      <div class="detail-content" v-if="currentPlan">
        <div class="detail-info">
          <p><strong>编号:</strong> {{ currentPlan.code }}</p>
          <p><strong>创建时间:</strong> {{ currentPlan.time }}</p>
        </div>
        <div class="detail-locations">
          <h4>路径地点:</h4>
          <div class="location-list">
            <div v-for="(location, index) in getPlanLocations(currentPlan.jsons)" :key="location.id"
              class="location-detail-item">
              <span class="location-index">{{ index + 1 }}</span>
              <div class="location-detail">
                <div class="location-name">{{ location.name }}</div>
                <div class="location-address">{{ location.address }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </van-dialog>

    <!-- 住宿列表 -->
    <van-popup v-model:show="showHotelDialog" destroy-on-close position="bottom" title="住宿列表" class="hotels-dialog"
      :style="{ height: '60%' }">
      <van-nav-bar title="住宿列表" @click-right="addNewHotel">
        <template #right>
          <van-button type="primary" size="small">添加</van-button>
        </template>
      </van-nav-bar>
      <div class="hotels-content">
        <div class="hotel-item " v-for="(hotel, hotel_key) in hotelsList" :key="hotel_key">
          <div class="flex items-center relative">
            <img class="hotel-img" :src="img_url(hotel.img)" alt="没有图片" @click="look(img_url(hotel.img))">
            <div class="hotel-name pl-4">{{ hotel.title }}</div>
            <div class=" absolute right-0 flex items-center">
              <div class="pr-2 flex items-center">
                <van-button type="primary" size="mini" icon="edit" @click="editHotel(hotel)" class="edit-btn" />
              </div>
              <van-button type="danger" size="mini" icon="cross" @click="removeHotel(hotel)" class="remove-btn" />
            </div>
          </div>
          <van-divider />
        </div>
        <van-empty description="没有数据了" v-if="hotelsList.length === 0" />
      </div>
    </van-popup>

    <!-- 住宿编辑 -->
    <van-dialog v-model:show="showHotelEditDialog" :title="hotelForm.id ? '编辑住宿' : '添加住宿'" show-cancel-button
      @confirm="saveHotel" @cancel="resetHotelForm" :close-on-click-overlay="false" class="hotel-edit-dialog">
      <div class="form-content">
        <van-field v-model="hotelForm.title" label="住宿名称" placeholder="请输入住宿名称" required />

        <!-- 关联地点选择 -->
        <van-field v-model="selectedLocationName" label="关联地点" placeholder="请选择关联地点" readonly required
          @click="showLocationPicker = true" />


        <van-field label="图片">
          <template #input>
            <!-- 图片上传 -->
            <van-uploader :after-read="afterRead" :deletable="false" v-model="fileList" :max-count="2" />
          </template>
        </van-field>

        <!-- 地点选择器弹窗 -->
        <van-popup v-model:show="showLocationPicker" position="bottom">
          <div class="location-picker-content">
            <van-radio-group v-model="selectedLocationId">
              <div v-for="location in availableLocations" :key="location.id" class="location-option">
                <van-radio :name="location.id" @click="selectLocation(location)">
                  <div class="location-info">
                    <div class="location-name">{{ location.name }}</div>
                    <div class="location-address">{{ location.address }}</div>
                  </div>
                </van-radio>
              </div>
            </van-radio-group>
          </div>
        </van-popup>
      </div>
    </van-dialog>

    <!-- 美食列表 -->
    <van-popup v-model:show="showFoodsDialog" destroy-on-close position="bottom" title="美食列表" class="foods-dialog"
      :style="{ height: '60%' }">
      <van-nav-bar title="美食列表" @click-right="addNewFood">
        <template #right>
          <van-button type="primary" size="small">添加</van-button>
        </template>
      </van-nav-bar>
      <div class="foods-content">
        <div class="food-item " v-for="(food, food_key) in foodsList" :key="food_key">
          <div class="flex items-center relative">
            <img class="food-img" :src="img_url(food.img)" alt="没有图片" @click="look(img_url(food.img))">
            <div class="food-name pl-4">{{ food.title }}</div>
            <div class=" absolute right-0 flex items-center">
              <div class="pr-2 flex items-center">
                <van-button type="primary" size="mini" icon="edit" @click="editFood(food)" class="edit-btn" />
              </div>
              <van-button type="danger" size="mini" icon="cross" @click="removeFood(food)" class="remove-btn" />
            </div>
          </div>
          <van-divider />
        </div>
        <van-empty description="没有数据了" v-if="foodsList.length === 0" />
      </div>
    </van-popup>

    <!-- 美食编辑 -->
    <van-dialog v-model:show="showFoodEditDialog" :title="foodForm.id ? '编辑美食' : '添加美食'" show-cancel-button
      @confirm="saveFood" @cancel="resetFoodForm" :close-on-click-overlay="false" class="food-edit-dialog">
      <div class="form-content">
        <van-field v-model="foodForm.title" label="美食名称" placeholder="请输入美食名称" required />

        <!-- 关联地点选择 -->
        <van-field v-model="selectedFoodLocationName" label="关联地点" placeholder="请选择关联地点" readonly required
          @click="showFoodLocationPicker = true" />

        <van-field label="图片">
          <template #input>
            <!-- 图片上传 -->
            <van-uploader :after-read="afterReadFood" :deletable="false" v-model="fileListFood" :max-count="2" />
          </template>
        </van-field>

        <!-- 地点选择器弹窗 -->
        <van-popup v-model:show="showFoodLocationPicker" position="bottom">
          <div class="location-picker-content">
            <van-radio-group v-model="selectedFoodLocationId">
              <div v-for="location in availableLocations" :key="location.id" class="location-option">
                <van-radio :name="location.id" @click="selectFoodLocation(location)">
                  <div class="location-info">
                    <div class="location-name">{{ location.name }}</div>
                    <div class="location-address">{{ location.address }}</div>
                  </div>
                </van-radio>
              </div>
            </van-radio-group>
          </div>
        </van-popup>
      </div>
    </van-dialog>
  </div>

</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch } from "vue";
import { showToast, showConfirmDialog } from "vant";
import { locationlist } from "@/api/locations";
import { pathslist, pathssave, pathsdel } from "@/api/paths";
import { foodslist, foodsdel, foodssave } from "@/api/foods";
import { hotelslist, hotelsdel, hotelssave } from "@/api/hotels";
import { upload } from "@/api/user";
import { useRouter } from 'vue-router'
import { showImagePreview } from 'vant';

// 接口类型定义
interface Location {
  id: number;
  name: string;
  description: string;
  address: string;
  lng: string;
  lat: string;
  time: string;
  uid: number;
}

interface Plan {
  id?: number;
  code: string;
  title: string;
  jsons: string;
  time: string;
  uid: number;
}



// 响应式数据
const plansList = ref<Plan[]>([]);
const availableLocations = ref<Location[]>([]);
const selectedLocations = ref<Location[]>([]);
const selectedLocationIds = computed(() => selectedLocations.value.map(loc => loc.id));

// 弹窗控制
const showPlanPopup = ref(false);
const showCreateDialog = ref(false);
const showDetailDialog = ref(false);
const editingPlan = ref<Plan | null>(null);
const currentPlan = ref<Plan | null>(null);


const router = useRouter()


//拼接图片链接
const img_url = computed(() => (url: string) => {
  return url ? import.meta.env.VITE_API_FILE_URL + url : ''
})

// 预览
const look = (url: string) => {
  showImagePreview([url]);
}

const showPlanPopupOpen = () => {
  showPlanPopup.value = true
}

const showCreateDialogOpen = () => {
  showCreateDialog.value = true
}



// 表单数据
const formData = ref({
  title: "",
});

// 生成随机编号
const generateCode = (): string => {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 7);
  return `${timestamp}_${random}`.toUpperCase();
};

// 获取路径规划列表
const loadPlansList = async () => {
  try {
    const response = await pathslist({
      limit: 999,
      offset: 1,
    });
    if (response.success) {
      plansList.value = response.data.data || [];
    }
  } catch (error) {
    showToast("获取路径规划列表失败");
  }
};

// 获取可用地点列表
const loadAvailableLocations = async () => {
  try {
    const response = await locationlist({
      limit: 999,
      offset: 1,
    });
    if (response.success) {
      availableLocations.value = response.data.data || [];
    }
  } catch (error) {
    showToast("获取地点列表失败");
  }
};

// 切换地点选择
const toggleLocation = (location: Location) => {
  const index = selectedLocations.value.findIndex(loc => loc.id === location.id);
  if (index > -1) {
    selectedLocations.value.splice(index, 1);
  } else {
    selectedLocations.value.push(location);
  }
};

// 移除已选地点
const removeLocation = (index: number) => {
  selectedLocations.value.splice(index, 1);
};

// 向上移动地点
const moveLocationUp = (index: number) => {
  if (index > 0) {
    const temp = selectedLocations.value[index];
    selectedLocations.value[index] = selectedLocations.value[index - 1];
    selectedLocations.value[index - 1] = temp;
  }
};

// 向下移动地点
const moveLocationDown = (index: number) => {
  if (index < selectedLocations.value.length - 1) {
    const temp = selectedLocations.value[index];
    selectedLocations.value[index] = selectedLocations.value[index + 1];
    selectedLocations.value[index + 1] = temp;
  }
};

// 获取路径中的地点数量
const getPlanLocationsCount = (jsons: string): number => {
  try {
    const locations = JSON.parse(jsons || "[]");
    return Array.isArray(locations) ? locations.length : 0;
  } catch {
    return 0;
  }
};

// 获取路径中的地点列表
const getPlanLocations = (jsons: string): Location[] => {
  try {
    const locations = JSON.parse(jsons || "[]");
    return Array.isArray(locations) ? locations : [];
  } catch {
    return [];
  }
};

// 保存路径规划
const savePlan = async () => {
  if (!formData.value.title.trim()) {
    showToast("请输入路径名称");
    return;
  }

  if (selectedLocations.value.length === 0) {
    showToast("请至少选择一个地点");
    return;
  }

  try {
    const planData = {
      title: formData.value.title,
      code: editingPlan.value?.code || generateCode(),
      jsons: JSON.stringify(selectedLocations.value),
      ...(editingPlan.value?.id && { id: editingPlan.value.id }),
    };

    const response = await pathssave(planData);
    if (response.success) {
      showToast(editingPlan.value ? "更新成功" : "创建成功");
      showCreateDialog.value = false;
      resetForm();
      loadPlansList();
    } else {
      showToast("保存失败");
    }
  } catch (error) {
    showToast("保存失败");
  }
};

// 查看路径
const planLint = (plan: Plan) => {
  router.push(`/route/${plan.code}`)
};

// 编辑路径规划
const editPlan = (plan: Plan) => {
  editingPlan.value = plan;
  formData.value.title = plan.title;
  selectedLocations.value = getPlanLocations(plan.jsons);
  showCreateDialog.value = true;
};

// 删除路径规划
const deletePlan = async (id: number) => {
  console.log(id);

  try {
    await showConfirmDialog({
      title: "确认删除",
      message: "确定要删除这个路径规划吗？",
    });

    const response = await pathsdel({ id });
    if (response.success) {
      showToast("删除成功");
      loadPlansList();
    } else {
      showToast("删除失败");
    }
  } catch (error) {
    // 用户取消删除
  }
};
//开放以供父组件调用
defineExpose({
  deletePlan
})

// 查看路径详情
const viewPlanDetail = (plan: Plan) => {
  currentPlan.value = plan;
  showDetailDialog.value = true;
};

// 重置表单
const resetForm = () => {
  formData.value.title = "";
  selectedLocations.value = [];
  editingPlan.value = null;
};


const pid = ref<number | null>(null)
//住宿相关
const showHotelDialog = ref(false)
const hotelsList = ref<any[]>([])
const getHotelsList = async () => {
  try {
    const response = await hotelslist({
      pid: pid.value,
      limit: 999,
      offset: 1
    })
    if (response.success) {
      hotelsList.value = response.data.data || []
    }
    showHotelDialog.value = true
  } catch (error) {
    console.error('获取住宿列表失败:', error)
  }
}

const showHotelEditDialog = ref(false)
const showLocationPicker = ref(false)
const selectedLocationId = ref<number | null>(null)
const selectedLocationName = ref('')
const hotelForm = ref({
  id: undefined as number | undefined,
  title: '',
  pid: null as number | null,
  lid: null as number | null,
  img: ''
})
//编辑住宿
const editHotel = async (item: any) => {
  hotelForm.value = { ...item }
  selectedLocationId.value = item.lid
  selectedLocationName.value = getLocationNameById(item.lid)
  fileList.value = [{
    url: item.img ? import.meta.env.VITE_API_FILE_URL + item.img : ''
  }]
  console.log(fileList.value);

  showHotelEditDialog.value = true
}
//选择地点
const selectLocation = (location: Location) => {
  selectedLocationId.value = location.id
  selectedLocationName.value = location.name
  hotelForm.value.lid = location.id
  showLocationPicker.value = false
}
//获取地点名称
const getLocationNameById = (id: number | null): string => {
  if (!id) return ''
  const location = availableLocations.value.find(loc => loc.id === id)
  return location ? location.name : ''
}
//重置住宿表单
const resetHotelForm = () => {
  hotelForm.value = {
    id: undefined,
    title: '',
    pid: null,
    lid: null,
    img: ''
  }
  selectedLocationId.value = null
  selectedLocationName.value = ''
  showHotelEditDialog.value = false
  fileList.value = []
}
//上传图片(住宿)
const fileList = ref<any[]>([])
const afterRead = (file: any) => {
  if (file.file.size > 10 * 1024 * 1024) {
    showToast('文件大小超过10MB')
    return
  }
  let form = new FormData()
  form.append('file', file.file)
  upload(form).then((res: any) => {
    console.log(res);
    if (res.code == 200) {
      fileList.value = [{
        url: `${import.meta.env.VITE_API_FILE_URL}${res.data.files[0].filePath}`
      }]
      hotelForm.value.img = res.data.files[0].filePath
    }
  })
}

//保存住宿
const saveHotel = async () => {
  if (!hotelForm.value.title.trim()) {
    showToast('请输入住宿名称')
    return
  }

  if (!hotelForm.value.lid) {
    showToast('请选择关联地点')
    return
  }

  try {
    const response = await hotelssave({
      ...hotelForm.value,
      pid: pid.value
    })
    if (response.success) {
      showToast('保存成功')
      showHotelEditDialog.value = false
      resetHotelForm()
      getHotelsList()
    } else {
      showToast('保存失败')
    }
  } catch (error) {
    console.error('保存住宿失败:', error)
    showToast('保存失败')
  }
}
//删除住宿
const removeHotel = async (item: any) => {
  try {
    const response = await hotelsdel({
      id: item.id,
    })
    if (response.success) {
      showToast('删除成功')
      getHotelsList()
    } else {
      showToast('删除失败')
    }
  } catch (error) {
    console.error('删除住宿失败:', error)
  }
}
//添加新住宿
const addNewHotel = () => {
  resetHotelForm();
  showHotelEditDialog.value = true;
};

//美食相关
const showFoodsDialog = ref(false)
const foodsList = ref<any[]>([])
const getFoodsList = async () => {
  try {
    const response = await foodslist({
      pid: pid.value,
      limit: 999,
      offset: 1
    })
    if (response.success) {
      foodsList.value = response.data.data || []
    }
    showFoodsDialog.value = true
  } catch (error) {
    console.error('获取美食列表失败:', error)
  }
}

const showFoodEditDialog = ref(false)
const showFoodLocationPicker = ref(false)
const selectedFoodLocationId = ref<number | null>(null)
const selectedFoodLocationName = ref('')
const foodForm = ref({
  id: undefined as number | undefined,
  title: '',
  pid: null as number | null,
  lid: null as number | null,
  img: ''
})
//编辑美食
const editFood = async (item: any) => {
  foodForm.value = { ...item }
  selectedFoodLocationId.value = item.lid
  selectedFoodLocationName.value = getLocationNameById(item.lid)
  fileListFood.value = [{
    url: item.img ? import.meta.env.VITE_API_FILE_URL + item.img : ''
  }]
  showFoodEditDialog.value = true
}
//选择地点(美食)
const selectFoodLocation = (location: Location) => {
  selectedFoodLocationId.value = location.id
  selectedFoodLocationName.value = location.name
  foodForm.value.lid = location.id
  showFoodLocationPicker.value = false
}
//重置美食表单
const resetFoodForm = () => {
  foodForm.value = {
    id: undefined,
    title: '',
    pid: null,
    lid: null,
    img: ''
  }
  selectedFoodLocationId.value = null
  selectedFoodLocationName.value = ''
  showFoodEditDialog.value = false
  fileListFood.value = []
}
//上传图片(美食)
const fileListFood = ref<any[]>([])
const afterReadFood = (file: any) => {
  if (file.file.size > 10 * 1024 * 1024) {
    showToast('文件大小超过10MB')
    return
  }
  let form = new FormData()
  form.append('file', file.file)
  upload(form).then((res: any) => {
    if (res.code == 200) {
      fileListFood.value = [{
        url: `${import.meta.env.VITE_API_FILE_URL}${res.data.files[0].filePath}`
      }]
      foodForm.value.img = res.data.files[0].filePath
    }
  })
}
//保存美食
const saveFood = async () => {
  if (!foodForm.value.title.trim()) {
    showToast('请输入美食名称')
    return
  }

  if (!foodForm.value.lid) {
    showToast('请选择关联地点')
    return
  }

  try {
    const response = await foodssave({
      ...foodForm.value,
      pid: pid.value
    })
    if (response.success) {
      showToast('保存成功')
      showFoodEditDialog.value = false
      resetFoodForm()
      getFoodsList()
    } else {
      showToast('保存失败')
    }
  } catch (error) {
    console.error('保存美食失败:', error)
    showToast('保存失败')
  }
}
//删除美食
const removeFood = async (item: any) => {
  try {
    const response = await foodsdel({
      id: item.id,
    })
    if (response.success) {
      showToast('删除成功')
      getFoodsList()
    } else {
      showToast('删除失败')
    }
  } catch (error) {
    console.error('删除美食失败:', error)
  }
}
//添加新美食
const addNewFood = () => {
  resetFoodForm();
  showFoodEditDialog.value = true;
};

// 页面加载完成
onMounted(() => {
  loadPlansList();
  loadAvailableLocations();
});

watch([showCreateDialog, showHotelEditDialog, showFoodEditDialog], ([val, val2, val3]) => {
  if (val || val2 || val3) {
    loadAvailableLocations()
  }
})

// 页面卸载
onBeforeUnmount(() => {
  // 清理工作
});
</script>

<style scoped lang="scss">
/* ===== 基础样式 ===== */
.plan-container {
  // background-color: #f5f5f5;
  // min-height: 100vh;
}

.header-actions {
  display: flex;
  justify-content: flex-end;
}

.plans-list {
  .plan-items {
    .plan-item {
      background: white;
      border-radius: 8px;
      margin-bottom: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
      }

      .plan-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .plan-title {
          margin: 0;
          font-weight: 600;
          color: #333;
        }

        .plan-actions {
          display: flex;
          gap: 8px;
        }
      }

      .plan-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #666;

        .plan-code {
          font-family: monospace;
          background: #f0f0f0;
          padding: 2px 6px;
          border-radius: 4px;
        }
      }

      .plan-locations {
        color: #999;

        .locations-count {
          background: #e8f4fd;
          color: #1989fa;
          padding: 2px 6px;
          border-radius: 4px;
        }
      }
    }
  }
}

/* ===== 弹窗样式 ===== */
.plan-dialog {
  .form-content {
    .locations-section {
      .section-title {
        font-weight: 600;
        color: #333;
      }

      .subtitle {
        color: #666;
      }

      .available-locations {
        .location-chips {
          display: flex;
          flex-wrap: wrap;

          .location-chip {
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
              transform: scale(1.05);
            }
          }
        }
      }

      .selected-locations {
        .sorted-locations {

          // 不显示滚动条
          &::-webkit-scrollbar {
            display: none;
          }

          .sorted-location-item {
            display: flex;
            align-items: center;
            background: #f0f8ff;
            border: 1px solid #1989fa;
            border-radius: 6px;
            transition: all 0.2s ease;

            &:hover {
              background: #e8f4fd;
            }

            .location-order {
              background: #1989fa;
              color: white;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              flex-shrink: 0;
            }

            .location-name {
              flex: 1;
            }

            .location-actions {
              display: flex;
              align-items: center;

              .remove-btn {
                background-color: #ff4444;
                border-color: #ff4444;
              }
            }
          }
        }
      }
    }
  }
}

.plan-popup {
  height: 100%;
  display: flex;
  flex-direction: column;

  .plan-content {
    flex: 1;
    overflow: hidden;
    background-color: #f5f5f5;
  }
}

.detail-dialog {
  .detail-content {
    .detail-info {
      p {
        margin: 8px 0;
        color: #333;
      }
    }

    .detail-locations {
      h4 {
        margin: 0 0 12px 0;
        color: #333;
      }

      .location-list {
        .location-detail-item {
          display: flex;
          align-items: center;
          background: #f8f9fa;
          border-radius: 6px;

          .location-index {
            background: #1989fa;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
          }

          .location-detail {
            flex: 1;

            .location-name {
              font-weight: 600;
              color: #333;
            }

            .location-address {
              color: #666;
            }
          }
        }
      }
    }
  }
}

/* ===== 住宿编辑弹窗样式 ===== */
.hotel-edit-dialog {
  .van-popup {
    overflow-y: hidden;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .form-content {
    padding: 20px;

    .van-field {
      margin-bottom: 16px;
    }
  }
}

/* ===== 美食编辑弹窗样式 ===== */
.food-edit-dialog {
  .van-popup {
    overflow-y: hidden;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .form-content {
    padding: 20px;

    .van-field {
      margin-bottom: 16px;
    }
  }
}

.location-picker-content {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;

  // 不显示滚动条
  &::-webkit-scrollbar {
    display: none;
  }

  .location-option {
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .location-info {
      margin-left: 12px;

      .location-name {
        font-weight: 600;
        color: #333;
        margin-bottom: 4px;
      }

      .location-address {
        color: #666;
        font-size: 14px;
      }
    }
  }
}

/* ===== PC端样式 (min-width: 769px) ===== */
@media (min-width: 769px) {
  .plan-container {
    padding: 20px;
  }

  .header-actions {
    margin-bottom: 16px;
  }

  .plan-actions {
    .van-button {
      padding: 6px 12px;
      font-size: 14px;
    }
  }

  .hotels-content {
    max-height: 600px;
    overflow-y: scroll;
    padding: 20px;

    // 不显示滚动条
    &::-webkit-scrollbar {
      display: none;
    }

    .hotel-item {
      .hotel-img {
        width: 130px;
        height: 100px;
        object-fit: cover;
        border-radius: 10px;
      }

      .hotel-name {
        font-size: 16px;
        width: 40%;
      }
    }
  }

  .foods-content {
    max-height: 600px;
    overflow-y: scroll;
    padding: 20px;

    &::-webkit-scrollbar {
      display: none;
    }

    .food-item {
      .food-img {
        width: 130px;
        height: 100px;
        object-fit: cover;
        border-radius: 10px;
      }

      .food-name {
        font-size: 16px;
        width: 40%;
      }
    }
  }

  .plans-list {

    .plan-items {
      .plan-item {
        padding: 20px;

        .plan-header {
          margin-bottom: 12px;

          .plan-title {
            font-size: 18px;
          }


        }

        .plan-info {
          margin-bottom: 8px;
          font-size: 14px;

          .plan-code {
            font-size: 12px;
          }
        }

        .plan-locations {
          font-size: 13px;
        }
      }
    }
  }

  ::v-deep(.van-dialog) {
    width: 500px
  }

  .plan-dialog {
    .form-content {
      padding: 20px;

      .locations-section {
        margin-top: 16px;

        .section-title {
          font-size: 16px;
          margin-bottom: 12px;
        }

        .subtitle {
          font-size: 14px;
          margin-bottom: 8px;
        }

        .available-locations {
          margin-bottom: 16px;

          .location-chips {

            // 不显示滚动条
            &::-webkit-scrollbar {
              display: none;
            }

            height: 200px;
            overflow-y: scroll;
            gap: 8px;

            .location-chip {
              border-radius: 6px;
              font-size: 14px;
              padding: 6px 12px;
            }
          }
        }

        .selected-locations {
          .sorted-locations {
            height: 300px;
            overflow-y: scroll;

            .sorted-location-item {
              padding: 12px 16px;
              margin-bottom: 8px;

              .location-order {
                width: 24px;
                height: 24px;
                font-size: 12px;
                margin-right: 12px;
              }

              .location-name {
                font-size: 16px;
                margin-right: 12px;
              }

              .location-actions {
                gap: 6px;

                .move-btn,
                .remove-btn {
                  min-width: 28px;
                  height: 28px;
                  padding: 4px 6px;

                  :deep(.van-button__text) {
                    font-size: 12px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .detail-dialog {
    .detail-content {
      padding: 20px;

      .detail-info {
        margin-bottom: 16px;

        p {
          font-size: 16px;
        }
      }

      .detail-locations {
        height: 300px;
        overflow-y: scroll;

        h4 {
          font-size: 16px;
        }

        .location-list {
          .location-detail-item {
            padding: 16px;
            margin-bottom: 8px;

            .location-index {
              width: 28px;
              height: 28px;
              font-size: 14px;
              margin-right: 16px;
            }

            .location-detail {
              .location-name {
                font-size: 16px;
                margin-bottom: 4px;
              }

              .location-address {
                font-size: 14px;
              }
            }
          }
        }
      }
    }
  }
}

/* ===== 移动端样式 (max-width: 768px) ===== */
@media (max-width: 768px) {
  .hotel-edit-dialog {
    .van-popup {
      overflow-y: hidden;
      height: 500px
    }

    .form-content {
      padding: 20px;

      .van-field {
        margin-bottom: 16px;
      }
    }
  }

  .food-edit-dialog {
    .van-popup {
      overflow-y: hidden;
      height: 500px
    }

    .form-content {
      padding: 20px;

      .van-field {
        margin-bottom: 16px;
      }
    }
  }

  .location-picker-content {
    padding: 80px;
    min-height: 1600px;

    ::v-deep .location-address {
      font-size: 60px !important
    }
  }

  .hotels-content {
    max-height: 2400px;
    overflow-y: scroll;
    padding: 80px;

    // 不显示滚动条
    &::-webkit-scrollbar {
      display: none;
    }

    .hotel-item {
      .hotel-img {
        width: 520px;
        height: 400px;
        object-fit: cover;
        border-radius: 40px;
      }

      .hotel-name {
        font-size: 68px;
        width: 40%;
      }
    }
  }

  .foods-content {
    max-height: 2400px;
    overflow-y: scroll;
    padding: 80px;

    &::-webkit-scrollbar {
      display: none;
    }

    .food-item {
      .food-img {
        width: 520px;
        height: 400px;
        object-fit: cover;
        border-radius: 40px;
      }

      .food-name {
        font-size: 68px;
        width: 40%;
      }
    }
  }

  .plan-container {
    padding: 48px;
  }

  .header-actions {
    margin-bottom: 48px;
  }

  .plan-actions {
    align-self: flex-end;

    .van-button {
      font-size: 12px;
    }
  }

  .plans-list {
    .plan-items {
      .plan-item {
        padding: 64px 48px;

        .plan-header {
          // flex-direction: column;
          align-items: flex-start;
          gap: 48px;
          margin-bottom: 48px;

          .plan-title {
            font-size: 80px;
          }


        }

        .plan-info {
          margin-bottom: 32px;
          font-size: 64px;

          .plan-code {
            font-size: 60px;
            border-radius: 15px;
            padding: 16px 32px;
          }

        }

        .plan-locations {
          font-size: 60px;

          .locations-count {
            padding: 16px 32px;
            border-radius: 15px;
          }
        }
      }
    }
  }

  .plan-dialog {
    .form-content {
      padding: 64px;

      .locations-section {
        margin-top: 64px;

        .section-title {
          font-size: 72px;
          margin-bottom: 48px;
        }

        .subtitle {
          font-size: 64px;
          margin-bottom: 48px;
        }

        .available-locations {
          margin-bottom: 80px;

          .location-chips {
            height: 700px;
            overflow-y: scroll;
            gap: 48px;

            .location-chip {
              font-size: 65px;
              padding: 30px 50px;
            }
          }
        }

        .selected-locations {
          .sorted-locations {
            height: 800px;
            overflow-y: scroll;

            .sorted-location-item {
              padding: 30px 50px;
              margin-bottom: 48px;
              border-radius: 30px;

              .location-order {
                width: 100px;
                height: 100px;
                font-size: 50px;
                margin-right: 64px;
              }

              .location-name {
                font-size: 70px;
                margin-right: 64px;
              }

              .location-actions {
                gap: 6px;

                .move-btn,
                .remove-btn {
                  min-width: 100px;
                  height: 100px;
                  padding: 4px 6px;

                  :deep(.van-button__text) {
                    font-size: 12px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .detail-dialog {
    .detail-content {
      padding: 64px;

      .detail-info {
        margin-bottom: 64px;

        p {
          font-size: 64px;
        }
      }

      .detail-locations {
        height: 2000px;
        overflow-y: scroll;

        h4 {
          font-size: 64px;
        }

        .location-list {
          .location-detail-item {
            padding: 64px 48px;
            margin-bottom: 48px;

            .location-index {
              width: 128px;
              height: 128px;
              font-size: 64px;
              margin-right: 64px;
            }

            .location-detail {
              .location-name {
                font-size: 72px;
                margin-bottom: 24px;
              }

              .location-address {
                font-size: 64px;
              }
            }
          }
        }
      }
    }
  }
}
</style>

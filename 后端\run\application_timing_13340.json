[{"name": "Process Start", "start": 1755792609271, "end": 1755792609675, "duration": 404, "pid": 13340, "index": 0}, {"name": "Application Start", "start": 1755792609676, "end": 1755792610125, "duration": 449, "pid": 13340, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1755792609684, "end": 1755792609694, "duration": 10, "pid": 13340, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1755792609694, "end": 1755792609708, "duration": 14, "pid": 13340, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1755792609695, "end": 1755792609696, "duration": 1, "pid": 13340, "index": 4}, {"name": "Require(1) node_modules/egg-session/config/config.default.js", "start": 1755792609697, "end": 1755792609697, "duration": 0, "pid": 13340, "index": 5}, {"name": "Require(2) node_modules/egg-security/config/config.default.js", "start": 1755792609697, "end": 1755792609697, "duration": 0, "pid": 13340, "index": 6}, {"name": "Require(3) node_modules/egg-jsonp/config/config.default.js", "start": 1755792609698, "end": 1755792609698, "duration": 0, "pid": 13340, "index": 7}, {"name": "Require(4) node_modules/egg-onerror/config/config.default.js", "start": 1755792609698, "end": 1755792609698, "duration": 0, "pid": 13340, "index": 8}, {"name": "Require(5) node_modules/egg-i18n/config/config.default.js", "start": 1755792609699, "end": 1755792609699, "duration": 0, "pid": 13340, "index": 9}, {"name": "Require(6) node_modules/egg-watcher/config/config.default.js", "start": 1755792609699, "end": 1755792609699, "duration": 0, "pid": 13340, "index": 10}, {"name": "Require(7) node_modules/egg-schedule/config/config.default.js", "start": 1755792609700, "end": 1755792609700, "duration": 0, "pid": 13340, "index": 11}, {"name": "Require(8) node_modules/egg-multipart/config/config.default.js", "start": 1755792609700, "end": 1755792609700, "duration": 0, "pid": 13340, "index": 12}, {"name": "Require(9) node_modules/egg-development/config/config.default.js", "start": 1755792609700, "end": 1755792609701, "duration": 1, "pid": 13340, "index": 13}, {"name": "Require(10) node_modules/egg-logrotator/config/config.default.js", "start": 1755792609701, "end": 1755792609701, "duration": 0, "pid": 13340, "index": 14}, {"name": "Require(11) node_modules/egg-static/config/config.default.js", "start": 1755792609701, "end": 1755792609701, "duration": 0, "pid": 13340, "index": 15}, {"name": "Require(12) node_modules/egg-view/config/config.default.js", "start": 1755792609702, "end": 1755792609702, "duration": 0, "pid": 13340, "index": 16}, {"name": "Require(13) node_modules/egg-sequelize/config/config.default.js", "start": 1755792609702, "end": 1755792609702, "duration": 0, "pid": 13340, "index": 17}, {"name": "Require(14) node_modules/egg-cors/config/config.default.js", "start": 1755792609702, "end": 1755792609703, "duration": 1, "pid": 13340, "index": 18}, {"name": "Require(15) node_modules/egg-view-nunjucks/config/config.default.js", "start": 1755792609703, "end": 1755792609703, "duration": 0, "pid": 13340, "index": 19}, {"name": "Require(16) node_modules/egg/config/config.default.js", "start": 1755792609703, "end": 1755792609703, "duration": 0, "pid": 13340, "index": 20}, {"name": "Require(17) config/config.default.js", "start": 1755792609704, "end": 1755792609704, "duration": 0, "pid": 13340, "index": 21}, {"name": "Require(18) node_modules/egg-security/config/config.local.js", "start": 1755792609705, "end": 1755792609705, "duration": 0, "pid": 13340, "index": 22}, {"name": "Require(19) node_modules/egg-watcher/config/config.local.js", "start": 1755792609706, "end": 1755792609706, "duration": 0, "pid": 13340, "index": 23}, {"name": "Require(20) node_modules/egg-view/config/config.local.js", "start": 1755792609707, "end": 1755792609707, "duration": 0, "pid": 13340, "index": 24}, {"name": "Require(21) node_modules/egg-view-nunjucks/config/config.local.js", "start": 1755792609707, "end": 1755792609707, "duration": 0, "pid": 13340, "index": 25}, {"name": "Require(22) node_modules/egg/config/config.local.js", "start": 1755792609708, "end": 1755792609708, "duration": 0, "pid": 13340, "index": 26}, {"name": "Load extend/application.js", "start": 1755792609709, "end": 1755792609738, "duration": 29, "pid": 13340, "index": 27}, {"name": "Require(23) node_modules/egg-session/app/extend/application.js", "start": 1755792609710, "end": 1755792609710, "duration": 0, "pid": 13340, "index": 28}, {"name": "Require(24) node_modules/egg-security/app/extend/application.js", "start": 1755792609710, "end": 1755792609711, "duration": 1, "pid": 13340, "index": 29}, {"name": "Require(25) node_modules/egg-jsonp/app/extend/application.js", "start": 1755792609711, "end": 1755792609714, "duration": 3, "pid": 13340, "index": 30}, {"name": "Require(26) node_modules/egg-schedule/app/extend/application.js", "start": 1755792609714, "end": 1755792609717, "duration": 3, "pid": 13340, "index": 31}, {"name": "Require(27) node_modules/egg-logrotator/app/extend/application.js", "start": 1755792609717, "end": 1755792609718, "duration": 1, "pid": 13340, "index": 32}, {"name": "Require(28) node_modules/egg-view/app/extend/application.js", "start": 1755792609718, "end": 1755792609719, "duration": 1, "pid": 13340, "index": 33}, {"name": "Require(29) node_modules/egg-view-nunjucks/app/extend/application.js", "start": 1755792609720, "end": 1755792609737, "duration": 17, "pid": 13340, "index": 34}, {"name": "Load extend/request.js", "start": 1755792609738, "end": 1755792609741, "duration": 3, "pid": 13340, "index": 35}, {"name": "Require(30) node_modules/egg/app/extend/request.js", "start": 1755792609739, "end": 1755792609740, "duration": 1, "pid": 13340, "index": 36}, {"name": "Load extend/response.js", "start": 1755792609741, "end": 1755792609745, "duration": 4, "pid": 13340, "index": 37}, {"name": "Require(31) node_modules/egg/app/extend/response.js", "start": 1755792609742, "end": 1755792609743, "duration": 1, "pid": 13340, "index": 38}, {"name": "Load extend/context.js", "start": 1755792609745, "end": 1755792609764, "duration": 19, "pid": 13340, "index": 39}, {"name": "Require(32) node_modules/egg-security/app/extend/context.js", "start": 1755792609745, "end": 1755792609750, "duration": 5, "pid": 13340, "index": 40}, {"name": "Require(33) node_modules/egg-jsonp/app/extend/context.js", "start": 1755792609751, "end": 1755792609751, "duration": 0, "pid": 13340, "index": 41}, {"name": "Require(34) node_modules/egg-i18n/app/extend/context.js", "start": 1755792609752, "end": 1755792609752, "duration": 0, "pid": 13340, "index": 42}, {"name": "Require(35) node_modules/egg-multipart/app/extend/context.js", "start": 1755792609752, "end": 1755792609761, "duration": 9, "pid": 13340, "index": 43}, {"name": "Require(36) node_modules/egg-view/app/extend/context.js", "start": 1755792609762, "end": 1755792609762, "duration": 0, "pid": 13340, "index": 44}, {"name": "Require(37) node_modules/egg/app/extend/context.js", "start": 1755792609762, "end": 1755792609763, "duration": 1, "pid": 13340, "index": 45}, {"name": "Load extend/helper.js", "start": 1755792609764, "end": 1755792609775, "duration": 11, "pid": 13340, "index": 46}, {"name": "Require(38) node_modules/egg-security/app/extend/helper.js", "start": 1755792609765, "end": 1755792609773, "duration": 8, "pid": 13340, "index": 47}, {"name": "Require(39) node_modules/egg/app/extend/helper.js", "start": 1755792609774, "end": 1755792609774, "duration": 0, "pid": 13340, "index": 48}, {"name": "Load app.js", "start": 1755792609775, "end": 1755792609793, "duration": 18, "pid": 13340, "index": 49}, {"name": "Require(40) node_modules/egg-session/app.js", "start": 1755792609775, "end": 1755792609775, "duration": 0, "pid": 13340, "index": 50}, {"name": "Require(41) node_modules/egg-security/app.js", "start": 1755792609776, "end": 1755792609776, "duration": 0, "pid": 13340, "index": 51}, {"name": "Require(42) node_modules/egg-onerror/app.js", "start": 1755792609777, "end": 1755792609781, "duration": 4, "pid": 13340, "index": 52}, {"name": "Require(43) node_modules/egg-i18n/app.js", "start": 1755792609781, "end": 1755792609785, "duration": 4, "pid": 13340, "index": 53}, {"name": "Require(44) node_modules/egg-watcher/app.js", "start": 1755792609786, "end": 1755792609789, "duration": 3, "pid": 13340, "index": 54}, {"name": "Require(45) node_modules/egg-schedule/app.js", "start": 1755792609789, "end": 1755792609790, "duration": 1, "pid": 13340, "index": 55}, {"name": "Require(46) node_modules/egg-multipart/app.js", "start": 1755792609790, "end": 1755792609790, "duration": 0, "pid": 13340, "index": 56}, {"name": "Require(47) node_modules/egg-development/app.js", "start": 1755792609791, "end": 1755792609791, "duration": 0, "pid": 13340, "index": 57}, {"name": "Require(48) node_modules/egg-logrotator/app.js", "start": 1755792609791, "end": 1755792609791, "duration": 0, "pid": 13340, "index": 58}, {"name": "Require(49) node_modules/egg-static/app.js", "start": 1755792609791, "end": 1755792609791, "duration": 0, "pid": 13340, "index": 59}, {"name": "Require(50) node_modules/egg-sequelize/app.js", "start": 1755792609792, "end": 1755792609792, "duration": 0, "pid": 13340, "index": 60}, {"name": "Require(51) node_modules/egg-cors/app.js", "start": 1755792609792, "end": 1755792609792, "duration": 0, "pid": 13340, "index": 61}, {"name": "Require(52) node_modules/egg-view-nunjucks/app.js", "start": 1755792609792, "end": 1755792609792, "duration": 0, "pid": 13340, "index": 62}, {"name": "Require(53) app.js", "start": 1755792609793, "end": 1755792609793, "duration": 0, "pid": 13340, "index": 63}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1755792609798, "end": 1755792610110, "duration": 312, "pid": 13340, "index": 64}, {"name": "Load \"Symbol(model)\" to Application", "start": 1755792610021, "end": 1755792610032, "duration": 11, "pid": 13340, "index": 65}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1755792610033, "end": 1755792610124, "duration": 91, "pid": 13340, "index": 66}, {"name": "Did Load in app.js:didLoad", "start": 1755792610033, "end": 1755792610103, "duration": 70, "pid": 13340, "index": 67}, {"name": "Load Service", "start": 1755792610033, "end": 1755792610038, "duration": 5, "pid": 13340, "index": 68}, {"name": "Load \"service\" to Context", "start": 1755792610033, "end": 1755792610038, "duration": 5, "pid": 13340, "index": 69}, {"name": "Load Middleware", "start": 1755792610038, "end": 1755792610086, "duration": 48, "pid": 13340, "index": 70}, {"name": "Load \"middlewares\" to Application", "start": 1755792610038, "end": 1755792610080, "duration": 42, "pid": 13340, "index": 71}, {"name": "Load Controller", "start": 1755792610086, "end": 1755792610090, "duration": 4, "pid": 13340, "index": 72}, {"name": "Load \"controller\" to Application", "start": 1755792610086, "end": 1755792610090, "duration": 4, "pid": 13340, "index": 73}, {"name": "Load Router", "start": 1755792610090, "end": 1755792610092, "duration": 2, "pid": 13340, "index": 74}, {"name": "Require(54) app/router.js", "start": 1755792610090, "end": 1755792610090, "duration": 0, "pid": 13340, "index": 75}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1755792610090, "end": 1755792610103, "duration": 13, "pid": 13340, "index": 76}, {"name": "Will Ready in app.js:will<PERSON><PERSON>y", "start": 1755792610125, "end": 1755792610125, "duration": 0, "pid": 13340, "index": 77}]
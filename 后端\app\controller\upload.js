'use strict';

const Controller = require('egg').Controller;
const fs = require('fs');
const path = require('path');

class UploadController extends Controller {
  /**
   * 文件上传
   */
  async upload() {
    const { ctx } = this;
    try {
      // 获取上传的文件
      const files = ctx.request.files;
      
      if (!files || files.length === 0) {
        ctx.body = {
          code: 400,
          message: '请选择要上传的文件',
          data: null
        };
        return;
      }

      const uploadedFiles = [];
      
      for (const file of files) {
        // 检查文件类型
        if (!this.ctx.service.upload.isAllowedFileType(file.filename)) {
          ctx.body = {
            code: 400,
            message: `不支持的文件类型: ${file.filename}`,
            data: null
          };
          return;
        }

        // // 检查文件大小
        // console.log(file.size);
        // if (!this.ctx.service.upload.isFileSizeAllowed(file.size)) {
        //   ctx.body = {
        //     code: 400,
        //     message: `文件大小超过限制: ${file.filename}`,
        //     data: null
        //   };
        //   return;
        // }

        // 生成唯一文件名
        const ext = path.extname(file.filename);
        const fileName = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}${ext}`;
        
        // 确保上传目录存在
        const uploadDir = path.join(this.app.baseDir, '/uploads');
        if (!fs.existsSync(uploadDir)) {
          fs.mkdirSync(uploadDir, { recursive: true });
        }
        
        // 移动文件到目标目录
        const targetPath = path.join(uploadDir, fileName);
        const reader = fs.createReadStream(file.filepath);
        const writer = fs.createWriteStream(targetPath);
        
        await new Promise((resolve, reject) => {
          reader.pipe(writer);
          reader.on('end', resolve);
          reader.on('error', reject);
        });
        
        // 保存文件信息到数据库
        const fileInfo = {
          originalName: file.filename,
          fileName: fileName,
          filePath: `/uploads/${fileName}`,
          fileSize: file.size,
          mimeType: file.mimeType,
          uploadTime: new Date()
        };
        
        // 这里可以调用service保存到数据库
        // const savedFile = await ctx.service.upload.saveFileInfo(fileInfo);
        
        uploadedFiles.push(fileInfo);
        
        // 清理临时文件
        fs.unlinkSync(file.filepath);
      }
      
      ctx.body = {
        code: 200,
        message: '文件上传成功',
        data: {
          files: uploadedFiles,
          count: uploadedFiles.length
        }
      };
      
    } catch (error) {
      ctx.logger.error('文件上传失败:', error);
      ctx.body = {
        code: 500,
        message: '文件上传失败',
        data: null
      };
    }
  }

  /**
   * 获取文件列表
   */
  async list() {
    const { ctx } = this;
    try {
      const uploadDir = path.join(this.app.baseDir, 'app/public/uploads');
      
      if (!fs.existsSync(uploadDir)) {
        ctx.body = {
          code: 200,
          message: '暂无文件',
          data: []
        };
        return;
      }
      
      const files = fs.readdirSync(uploadDir);
      const fileList = [];
      
      for (const file of files) {
        const filePath = path.join(uploadDir, file);
        const stats = fs.statSync(filePath);
        
        if (stats.isFile()) {
          fileList.push({
            fileName: file,
            filePath: `/uploads/${file}`,
            fileSize: stats.size,
            uploadTime: stats.mtime
          });
        }
      }
      
      ctx.body = {
        code: 200,
        message: '获取文件列表成功',
        data: fileList
      };
      
    } catch (error) {
      ctx.logger.error('获取文件列表失败:', error);
      ctx.body = {
        code: 500,
        message: '获取文件列表失败',
        data: null
      };
    }
  }

  /**
   * 删除文件
   */
  async delete() {
    const { ctx } = this;
    try {
      const { fileName } = ctx.request.body;
      
      if (!fileName) {
        ctx.body = {
          code: 400,
          message: '文件名不能为空',
          data: null
        };
        return;
      }
      
      const filePath = path.join(this.app.baseDir, 'app/public/uploads', fileName);
      
      if (!fs.existsSync(filePath)) {
        ctx.body = {
          code: 404,
          message: '文件不存在',
          data: null
        };
        return;
      }
      
      // 删除文件
      fs.unlinkSync(filePath);
      
      ctx.body = {
        code: 200,
        message: '文件删除成功',
        data: null
      };
      
    } catch (error) {
      ctx.logger.error('文件删除失败:', error);
      ctx.body = {
        code: 500,
        message: '文件删除失败',
        data: null
      };
    }
  }

  /**
   * 下载文件
   */
  async download() {
    const { ctx } = this;
    try {
      const { fileName } = ctx.params;
      
      if (!fileName) {
        ctx.body = {
          code: 400,
          message: '文件名不能为空',
          data: null
        };
        return;
      }
      
      const filePath = path.join(this.app.baseDir, 'app/public/uploads', fileName);
      
      if (!fs.existsSync(filePath)) {
        ctx.body = {
          code: 404,
          message: '文件不存在',
          data: null
        };
        return;
      }
      
      // 设置下载头
      ctx.attachment(fileName);
      ctx.body = fs.createReadStream(filePath);
      
    } catch (error) {
      ctx.logger.error('文件下载失败:', error);
      ctx.body = {
        code: 500,
        message: '文件下载失败',
        data: null
      };
    }
  }

  /**
   * 获取上传统计信息
   */
  async getStats() {
    const { ctx } = this;
    try {
      const stats = await ctx.service.upload.getUploadStats();
      
      ctx.body = {
        code: 200,
        message: '获取统计信息成功',
        data: stats
      };
      
    } catch (error) {
      ctx.logger.error('获取统计信息失败:', error);
      ctx.body = {
        code: 500,
        message: '获取统计信息失败',
        data: null
      };
    }
  }
}

module.exports = UploadController; 
// This file is created by egg-ts-helper@2.1.1
// Do not modify this file!!!!!!!!!
/* eslint-disable */

import 'egg';
import ExportCitys = require('../../../app/controller/citys');
import ExportFoods = require('../../../app/controller/foods');
import ExportHotels = require('../../../app/controller/hotels');
import ExportLocation = require('../../../app/controller/location');
import ExportPaths = require('../../../app/controller/paths');
import ExportUpload = require('../../../app/controller/upload');
import ExportUser = require('../../../app/controller/user');

declare module 'egg' {
  interface IController {
    citys: ExportCitys;
    foods: ExportFoods;
    hotels: ExportHotels;
    location: ExportLocation;
    paths: ExportPaths;
    upload: ExportUpload;
    user: ExportUser;
  }
}

2025-08-04 19:59:14,072 INFO 20076 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-04 19:59:16,435 ERROR 20076 [-/127.0.0.1/-/7ms GET /] nodejs.AssertionError: Can't find index.html from 
    at ViewManager.resolve (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\view_manager.js:74:5)
    at async [contextView#render] (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\context_view.js:52:22)
    at async Object.<anonymous> (C:\Users\<USER>\Desktop\H5地图规划\后端\app\router.js:33:5)
generatedMessage: false
code: "ERR_ASSERTION"
actual: undefined
expected: true
operator: "=="
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 20076
hostname: l

2025-08-04 19:59:41,198 INFO 12312 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-04 19:59:42,262 ERROR 12312 [-/127.0.0.1/-/5ms GET /] nodejs.AssertionError: Can't find index.html from 
    at ViewManager.resolve (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\view_manager.js:74:5)
    at async [contextView#render] (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\context_view.js:52:22)
    at async Object.<anonymous> (C:\Users\<USER>\Desktop\H5地图规划\后端\app\router.js:33:5)
generatedMessage: false
code: "ERR_ASSERTION"
actual: undefined
expected: true
operator: "=="
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 12312
hostname: l

2025-08-04 19:59:43,024 ERROR 12312 [-/127.0.0.1/-/1ms GET /] nodejs.AssertionError: Can't find index.html from 
    at ViewManager.resolve (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\view_manager.js:74:5)
    at async [contextView#render] (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\context_view.js:52:22)
    at async Object.<anonymous> (C:\Users\<USER>\Desktop\H5地图规划\后端\app\router.js:33:5)
generatedMessage: false
code: "ERR_ASSERTION"
actual: undefined
expected: true
operator: "=="
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 12312
hostname: l

2025-08-04 19:59:48,952 ERROR 12312 [-/127.0.0.1/-/1ms GET /] nodejs.AssertionError: Can't find index.html from 
    at ViewManager.resolve (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\view_manager.js:74:5)
    at async [contextView#render] (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\context_view.js:52:22)
    at async Object.<anonymous> (C:\Users\<USER>\Desktop\H5地图规划\后端\app\router.js:33:5)
generatedMessage: false
code: "ERR_ASSERTION"
actual: undefined
expected: true
operator: "=="
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 12312
hostname: l

2025-08-04 19:59:49,190 INFO 18400 [egg-sequelize](0ms) Executed (default): SELECT 1+1 AS result
2025-08-04 20:00:54,100 INFO 20220 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-04 20:00:54,592 ERROR 20220 [-/127.0.0.1/-/4ms GET /] nodejs.AssertionError: Can't find index.html from 
    at ViewManager.resolve (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\view_manager.js:74:5)
    at async [contextView#render] (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\context_view.js:52:22)
    at async Object.<anonymous> (C:\Users\<USER>\Desktop\H5地图规划\后端\app\router.js:33:5)
generatedMessage: false
code: "ERR_ASSERTION"
actual: undefined
expected: true
operator: "=="
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 20220
hostname: l

2025-08-04 20:00:54,762 ERROR 20220 [-/127.0.0.1/-/1ms GET /] nodejs.AssertionError: Can't find index.html from 
    at ViewManager.resolve (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\view_manager.js:74:5)
    at async [contextView#render] (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\context_view.js:52:22)
    at async Object.<anonymous> (C:\Users\<USER>\Desktop\H5地图规划\后端\app\router.js:33:5)
generatedMessage: false
code: "ERR_ASSERTION"
actual: undefined
expected: true
operator: "=="
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 20220
hostname: l

2025-08-04 20:00:54,911 ERROR 20220 [-/127.0.0.1/-/1ms GET /] nodejs.AssertionError: Can't find index.html from 
    at ViewManager.resolve (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\view_manager.js:74:5)
    at async [contextView#render] (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\context_view.js:52:22)
    at async Object.<anonymous> (C:\Users\<USER>\Desktop\H5地图规划\后端\app\router.js:33:5)
generatedMessage: false
code: "ERR_ASSERTION"
actual: undefined
expected: true
operator: "=="
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 20220
hostname: l

2025-08-04 20:01:01,656 INFO 22328 [egg-sequelize](0ms) Executed (default): SELECT 1+1 AS result
2025-08-04 20:01:03,653 ERROR 22328 [-/127.0.0.1/-/5ms GET /] nodejs.AssertionError: Can't find index.html from 
    at ViewManager.resolve (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\view_manager.js:74:5)
    at async [contextView#render] (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\context_view.js:52:22)
    at async Object.<anonymous> (C:\Users\<USER>\Desktop\H5地图规划\后端\app\router.js:33:5)
generatedMessage: false
code: "ERR_ASSERTION"
actual: undefined
expected: true
operator: "=="
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 22328
hostname: l

2025-08-04 20:01:04,435 ERROR 22328 [-/127.0.0.1/-/1ms GET /] nodejs.AssertionError: Can't find index.html from 
    at ViewManager.resolve (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\view_manager.js:74:5)
    at async [contextView#render] (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\context_view.js:52:22)
    at async Object.<anonymous> (C:\Users\<USER>\Desktop\H5地图规划\后端\app\router.js:33:5)
generatedMessage: false
code: "ERR_ASSERTION"
actual: undefined
expected: true
operator: "=="
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 22328
hostname: l

2025-08-04 20:01:04,620 ERROR 22328 [-/127.0.0.1/-/1ms GET /] nodejs.AssertionError: Can't find index.html from 
    at ViewManager.resolve (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\view_manager.js:74:5)
    at async [contextView#render] (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\context_view.js:52:22)
    at async Object.<anonymous> (C:\Users\<USER>\Desktop\H5地图规划\后端\app\router.js:33:5)
generatedMessage: false
code: "ERR_ASSERTION"
actual: undefined
expected: true
operator: "=="
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 22328
hostname: l

2025-08-04 20:01:30,411 INFO 2652 [egg-sequelize](0ms) Executed (default): SELECT 1+1 AS result
2025-08-04 20:01:41,656 INFO 5280 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-04 20:01:56,076 INFO 2076 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-04 20:02:26,082 INFO 17492 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-04 20:04:22,160 INFO 17492 [-/127.0.0.1/-/22ms POST /user/login] [egg-sequelize](4ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = '1' AND `user`.`pwd` = '1' LIMIT 1;
2025-08-04 20:04:57,475 INFO 17492 [-/127.0.0.1/-/5ms POST /user/login] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = '1' AND `user`.`pwd` = '1' LIMIT 1;
2025-08-04 20:05:01,931 INFO 17492 [-/127.0.0.1/-/1ms POST /user/login] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'yam' AND `user`.`pwd` = 'yam' LIMIT 1;
2025-08-04 20:05:58,821 INFO 17492 [-/127.0.0.1/-/5ms POST /user/login] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'yam' AND `user`.`pwd` = 'yam' LIMIT 1;
2025-08-04 20:06:43,227 INFO 17492 [-/127.0.0.1/-/5ms POST /user/login] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'yam' AND `user`.`pwd` = 'yam' LIMIT 1;
2025-08-04 20:07:26,753 INFO 17492 [-/127.0.0.1/-/5ms POST /user/login] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'yam' AND `user`.`pwd` = 'yam' LIMIT 1;
2025-08-04 20:07:38,568 INFO 17492 [-/127.0.0.1/-/5ms POST /user/login] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'yam222' AND `user`.`pwd` = 'yam222' LIMIT 1;
2025-08-04 20:08:35,489 INFO 17492 [-/127.0.0.1/-/8ms POST /user/login] [egg-sequelize](3ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'yam222' AND `user`.`pwd` = 'yam222' LIMIT 1;
2025-08-04 20:09:07,231 INFO 17492 [-/127.0.0.1/-/6ms POST /user/login] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'yam222' AND `user`.`pwd` = 'yam222' LIMIT 1;
2025-08-04 20:09:15,386 INFO 17492 [-/127.0.0.1/-/1ms POST /user/login] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'yam222' AND `user`.`pwd` = 'yam222' LIMIT 1;
2025-08-04 20:10:04,726 INFO 17492 [-/127.0.0.1/-/5ms POST /user/login] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'yam222' AND `user`.`pwd` = 'yam222' LIMIT 1;
2025-08-04 20:10:29,392 INFO 17492 [-/127.0.0.1/-/6ms POST /user/login] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'yam' AND `user`.`pwd` = 'yam' LIMIT 1;
2025-08-04 20:10:30,868 INFO 17492 [-/127.0.0.1/-/17ms POST /paths/list] [egg-sequelize](11ms) Executed (default): SELECT count(*) AS `count` FROM `paths` AS `paths` WHERE `paths`.`uid` = 1;
2025-08-04 20:10:30,874 INFO 17492 [-/127.0.0.1/-/23ms POST /paths/list] [egg-sequelize](5ms) Executed (default): SELECT `id`, `code`, `title`, `jsons`, `time`, `uid` FROM `paths` AS `paths` WHERE `paths`.`uid` = 1 ORDER BY `paths`.`id` DESC LIMIT 0, 100;
2025-08-04 20:10:30,884 INFO 17492 [-/127.0.0.1/-/27ms POST /location/list] [egg-sequelize](6ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-04 20:10:30,889 INFO 17492 [-/127.0.0.1/-/27ms POST /citys/list] [egg-sequelize](5ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-04 20:10:30,890 INFO 17492 [-/127.0.0.1/-/33ms POST /location/list] [egg-sequelize](7ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 100;
2025-08-04 20:10:30,891 INFO 17492 [-/127.0.0.1/-/29ms POST /citys/list] [egg-sequelize](7ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-04 20:10:30,903 INFO 17492 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-04 20:10:30,906 INFO 17492 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-04 20:10:36,151 INFO 17492 [-/127.0.0.1/-/3ms POST /user/login] [egg-sequelize](3ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'yam' AND `user`.`pwd` = 'yam' LIMIT 1;
2025-08-04 20:10:36,177 INFO 17492 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 100;
2025-08-04 20:10:36,178 INFO 17492 [-/127.0.0.1/-/5ms POST /paths/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `code`, `title`, `jsons`, `time`, `uid` FROM `paths` AS `paths` WHERE `paths`.`uid` = 1 ORDER BY `paths`.`id` DESC LIMIT 0, 100;
2025-08-04 20:10:36,178 INFO 17492 [-/127.0.0.1/-/5ms POST /paths/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `paths` AS `paths` WHERE `paths`.`uid` = 1;
2025-08-04 20:10:36,179 INFO 17492 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-04 20:10:36,180 INFO 17492 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-04 20:10:36,180 INFO 17492 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-04 20:10:36,186 INFO 17492 [-/127.0.0.1/-/1ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-04 20:10:36,188 INFO 17492 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-04 20:10:43,042 INFO 17492 [-/127.0.0.1/-/3ms POST /user/login] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'yam222' AND `user`.`pwd` = 'yam222' LIMIT 1;
2025-08-04 20:11:42,973 INFO 17492 [-/127.0.0.1/-/7ms POST /paths/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `paths` AS `paths` WHERE `paths`.`code` = 'PATH_MDSM0MKR_0PEGO' AND `paths`.`uid` = 1;
2025-08-04 20:11:42,973 INFO 17492 [-/127.0.0.1/-/7ms POST /paths/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `code`, `title`, `jsons`, `time`, `uid` FROM `paths` AS `paths` WHERE `paths`.`code` = 'PATH_MDSM0MKR_0PEGO' AND `paths`.`uid` = 1 ORDER BY `paths`.`id` DESC LIMIT 0, 1;
2025-08-21 23:37:46,386 INFO 33808 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-21 23:37:56,552 INFO 33808 [-/127.0.0.1/-/35ms POST /paths/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `paths` AS `paths` WHERE `paths`.`uid` = 1;
2025-08-21 23:37:56,553 INFO 33808 [-/127.0.0.1/-/36ms POST /paths/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `code`, `title`, `jsons`, `time`, `uid` FROM `paths` AS `paths` WHERE `paths`.`uid` = 1 ORDER BY `paths`.`id` DESC LIMIT 0, 999;
2025-08-21 23:37:56,556 INFO 33808 [-/127.0.0.1/-/18ms POST /location/list] [egg-sequelize](6ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-21 23:37:56,557 INFO 33808 [-/127.0.0.1/-/19ms POST /location/list] [egg-sequelize](7ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 999;
2025-08-21 23:37:56,558 INFO 33808 [-/127.0.0.1/-/14ms POST /citys/list] [egg-sequelize](7ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-21 23:37:56,559 INFO 33808 [-/127.0.0.1/-/15ms POST /citys/list] [egg-sequelize](7ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 9999;
2025-08-21 23:37:56,566 INFO 33808 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-21 23:37:56,567 INFO 33808 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 9999;
2025-08-21 23:37:57,580 INFO 33808 [-/127.0.0.1/-/4ms POST /paths/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `code`, `title`, `jsons`, `time`, `uid` FROM `paths` AS `paths` WHERE `paths`.`uid` = 1 ORDER BY `paths`.`id` DESC LIMIT 0, 999;
2025-08-21 23:37:57,581 INFO 33808 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-21 23:37:57,581 INFO 33808 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 999;
2025-08-21 23:37:57,582 INFO 33808 [-/127.0.0.1/-/6ms POST /paths/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `paths` AS `paths` WHERE `paths`.`uid` = 1;
2025-08-21 23:37:57,583 INFO 33808 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 9999;
2025-08-21 23:37:57,583 INFO 33808 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-21 23:37:57,588 INFO 33808 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 9999;
2025-08-21 23:37:57,589 INFO 33808 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-21 23:47:17,967 INFO 33808 [-/127.0.0.1/-/15ms POST /paths/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `paths` AS `paths` WHERE `paths`.`uid` = 1;
2025-08-21 23:47:17,967 INFO 33808 [-/127.0.0.1/-/15ms POST /paths/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `code`, `title`, `jsons`, `time`, `uid` FROM `paths` AS `paths` WHERE `paths`.`uid` = 1 ORDER BY `paths`.`id` DESC LIMIT 0, 999;
2025-08-21 23:47:17,969 INFO 33808 [-/127.0.0.1/-/13ms POST /location/list] [egg-sequelize](5ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-21 23:47:17,969 INFO 33808 [-/127.0.0.1/-/13ms POST /location/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 999;
2025-08-21 23:47:17,970 INFO 33808 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](5ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-21 23:47:17,970 INFO 33808 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 9999;
2025-08-21 23:47:17,980 INFO 33808 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 9999;
2025-08-21 23:47:17,981 INFO 33808 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-21 23:51:30,995 INFO 32440 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-21 23:51:32,108 INFO 30536 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-21 23:51:37,659 INFO 30744 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-21 23:52:07,601 INFO 30224 [egg-sequelize](0ms) Executed (default): SELECT 1+1 AS result
2025-08-21 23:52:11,399 INFO 30224 [-/127.0.0.1/-/20ms POST /paths/list] [egg-sequelize](5ms) Executed (default): SELECT count(*) AS `count` FROM `paths` AS `paths` WHERE `paths`.`uid` = 1;
2025-08-21 23:52:11,403 INFO 30224 [-/127.0.0.1/-/24ms POST /paths/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `code`, `title`, `jsons`, `time`, `uid` FROM `paths` AS `paths` WHERE `paths`.`uid` = 1 ORDER BY `paths`.`id` DESC LIMIT 0, 999;
2025-08-21 23:52:11,408 INFO 30224 [-/127.0.0.1/-/14ms POST /location/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 999;
2025-08-21 23:52:11,410 INFO 30224 [-/127.0.0.1/-/16ms POST /location/list] [egg-sequelize](7ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-21 23:52:11,411 INFO 30224 [-/127.0.0.1/-/15ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 9999;
2025-08-21 23:52:11,412 INFO 30224 [-/127.0.0.1/-/16ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-21 23:52:11,417 INFO 30224 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-21 23:52:11,417 INFO 30224 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 9999;
2025-08-21 23:52:19,032 INFO 30224 [-/127.0.0.1/-/6ms POST /citys/setdefault] [egg-sequelize](3ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `uid` = ?
2025-08-21 23:52:19,034 INFO 30224 [-/127.0.0.1/-/8ms POST /citys/setdefault] [egg-sequelize](1ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `id` = ?
2025-08-21 23:52:19,042 INFO 30224 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-21 23:52:19,042 INFO 30224 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 9999;
2025-08-21 23:52:19,050 INFO 30224 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-21 23:52:19,050 INFO 30224 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 9999;
2025-08-21 23:52:23,166 INFO 30224 [-/127.0.0.1/-/3ms POST /citys/setdefault] [egg-sequelize](1ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `uid` = ?
2025-08-21 23:52:23,168 INFO 30224 [-/127.0.0.1/-/5ms POST /citys/setdefault] [egg-sequelize](1ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `id` = ?
2025-08-21 23:52:23,176 INFO 30224 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-21 23:52:23,176 INFO 30224 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 9999;
2025-08-21 23:52:23,185 INFO 30224 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-21 23:52:23,185 INFO 30224 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 9999;
2025-08-21 23:52:25,357 INFO 30224 [-/127.0.0.1/-/5ms POST /hotels/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `hotels` AS `hotels` WHERE `hotels`.`pid` = 1 ORDER BY `hotels`.`id` DESC LIMIT 0, 999;
2025-08-21 23:52:25,357 INFO 30224 [-/127.0.0.1/-/5ms POST /hotels/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `hotels` AS `hotels` WHERE `hotels`.`pid` = 1;
2025-08-21 23:53:43,031 INFO 30224 [-/127.0.0.1/-/10ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-21 23:53:43,032 INFO 30224 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 9999;
2025-08-21 23:53:43,033 INFO 30224 [-/127.0.0.1/-/18ms POST /paths/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `paths` AS `paths` WHERE `paths`.`uid` = 1;
2025-08-21 23:53:43,034 INFO 30224 [-/127.0.0.1/-/19ms POST /paths/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `code`, `title`, `jsons`, `time`, `uid` FROM `paths` AS `paths` WHERE `paths`.`uid` = 1 ORDER BY `paths`.`id` DESC LIMIT 0, 999;
2025-08-21 23:53:43,034 INFO 30224 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-21 23:53:43,036 INFO 30224 [-/127.0.0.1/-/15ms POST /location/list] [egg-sequelize](5ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 999;
2025-08-21 23:53:43,042 INFO 30224 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-21 23:53:43,044 INFO 30224 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 9999;
2025-08-21 23:54:22,637 INFO 30224 [-/127.0.0.1/-/6ms POST /hotels/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `hotels` AS `hotels` WHERE `hotels`.`pid` = 1 ORDER BY `hotels`.`id` DESC LIMIT 0, 999;
2025-08-21 23:54:22,638 INFO 30224 [-/127.0.0.1/-/7ms POST /hotels/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `hotels` AS `hotels` WHERE `hotels`.`pid` = 1;
2025-08-21 23:54:24,195 INFO 30224 [-/127.0.0.1/-/4ms POST /foods/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `foods` AS `foods` WHERE `foods`.`pid` = 1 ORDER BY `foods`.`id` DESC LIMIT 0, 999;
2025-08-21 23:54:24,195 INFO 30224 [-/127.0.0.1/-/4ms POST /foods/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`pid` = 1;
2025-08-21 23:54:28,073 INFO 30224 [-/127.0.0.1/-/5ms POST /paths/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `paths` AS `paths` WHERE `paths`.`code` = 'PATH_MDSM0MKR_0PEGO' AND `paths`.`uid` = 1;
2025-08-21 23:54:28,073 INFO 30224 [-/127.0.0.1/-/5ms POST /paths/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `code`, `title`, `jsons`, `time`, `uid` FROM `paths` AS `paths` WHERE `paths`.`code` = 'PATH_MDSM0MKR_0PEGO' AND `paths`.`uid` = 1 ORDER BY `paths`.`id` DESC LIMIT 0, 1;
2025-08-21 23:56:13,709 INFO 30224 [-/127.0.0.1/-/7ms POST /foods/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO' ORDER BY `foods`.`id` DESC LIMIT 0, 999;
2025-08-21 23:56:13,712 ERROR 30224 [-/127.0.0.1/-/8ms POST /foods/list] nodejs.SequelizeDatabaseError: Unknown column 'foods.code' in 'where clause'
    at Query.run (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\dialects\mysql\query.js:52:25)
    at C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async MySQLQueryInterface.select (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\dialects\abstract\query-interface.js:407:12)
    at async foods.findAll (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1140:21)
    at async Promise.all (index 1)
    at async foods.findAndCountAll (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1322:27)
    at async Location.paginate (C:\Users\<USER>\Desktop\H5地图规划\后端\app\model\foods.js:31:20)
    at async FoodsController.list (C:\Users\<USER>\Desktop\H5地图规划\后端\app\controller\foods.js:74:13)
    at async C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-development\app\middleware\egg_loader_trace.js:7:50
name: "SequelizeDatabaseError"
parent: {"code":"ER_BAD_FIELD_ERROR","errno":1054,"sqlState":"42S22","sqlMessage":"Unknown column 'foods.code' in 'where clause'","sql":"SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO' ORDER BY `foods`.`id` DESC LIMIT 0, 999;"}
original: {"code":"ER_BAD_FIELD_ERROR","errno":1054,"sqlState":"42S22","sqlMessage":"Unknown column 'foods.code' in 'where clause'","sql":"SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO' ORDER BY `foods`.`id` DESC LIMIT 0, 999;"}
sql: "SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO' ORDER BY `foods`.`id` DESC LIMIT 0, 999;"
parameters: {}
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 30224
hostname: l

2025-08-21 23:56:13,713 INFO 30224 [-/127.0.0.1/-/11ms POST /foods/list] [egg-sequelize](7ms) Executed (default): SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO';
2025-08-21 23:56:26,155 INFO 30224 [-/127.0.0.1/-/5ms POST /foods/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO';
2025-08-21 23:56:26,157 ERROR 30224 [-/127.0.0.1/-/6ms POST /foods/list] nodejs.SequelizeDatabaseError: Unknown column 'foods.code' in 'where clause'
    at Query.run (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\dialects\mysql\query.js:52:25)
    at C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async MySQLQueryInterface.rawSelect (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\dialects\abstract\query-interface.js:434:18)
    at async foods.aggregate (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1277:19)
    at async foods.count (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1306:20)
    at async Promise.all (index 0)
    at async foods.findAndCountAll (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1322:27)
    at async Location.paginate (C:\Users\<USER>\Desktop\H5地图规划\后端\app\model\foods.js:31:20)
    at async FoodsController.list (C:\Users\<USER>\Desktop\H5地图规划\后端\app\controller\foods.js:74:13)
name: "SequelizeDatabaseError"
parent: {"code":"ER_BAD_FIELD_ERROR","errno":1054,"sqlState":"42S22","sqlMessage":"Unknown column 'foods.code' in 'where clause'","sql":"SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO';"}
original: {"code":"ER_BAD_FIELD_ERROR","errno":1054,"sqlState":"42S22","sqlMessage":"Unknown column 'foods.code' in 'where clause'","sql":"SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO';"}
sql: "SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO';"
parameters: {}
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 30224
hostname: l

2025-08-21 23:56:26,157 INFO 30224 [-/127.0.0.1/-/7ms POST /foods/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO' ORDER BY `foods`.`id` DESC LIMIT 0, 999;
2025-08-21 23:56:28,677 INFO 30224 [-/127.0.0.1/-/2ms POST /foods/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO';
2025-08-21 23:56:28,678 ERROR 30224 [-/127.0.0.1/-/2ms POST /foods/list] nodejs.SequelizeDatabaseError: Unknown column 'foods.code' in 'where clause'
    at Query.run (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\dialects\mysql\query.js:52:25)
    at C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async MySQLQueryInterface.rawSelect (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\dialects\abstract\query-interface.js:434:18)
    at async foods.aggregate (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1277:19)
    at async foods.count (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1306:20)
    at async Promise.all (index 0)
    at async foods.findAndCountAll (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1322:27)
    at async Location.paginate (C:\Users\<USER>\Desktop\H5地图规划\后端\app\model\foods.js:31:20)
    at async FoodsController.list (C:\Users\<USER>\Desktop\H5地图规划\后端\app\controller\foods.js:74:13)
name: "SequelizeDatabaseError"
parent: {"code":"ER_BAD_FIELD_ERROR","errno":1054,"sqlState":"42S22","sqlMessage":"Unknown column 'foods.code' in 'where clause'","sql":"SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO';"}
original: {"code":"ER_BAD_FIELD_ERROR","errno":1054,"sqlState":"42S22","sqlMessage":"Unknown column 'foods.code' in 'where clause'","sql":"SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO';"}
sql: "SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO';"
parameters: {}
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 30224
hostname: l

2025-08-21 23:56:28,679 INFO 30224 [-/127.0.0.1/-/4ms POST /foods/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO' ORDER BY `foods`.`id` DESC LIMIT 0, 999;
2025-08-21 23:56:32,849 INFO 30224 [-/127.0.0.1/-/2ms POST /foods/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO';
2025-08-21 23:56:32,851 ERROR 30224 [-/127.0.0.1/-/3ms POST /foods/list] nodejs.SequelizeDatabaseError: Unknown column 'foods.code' in 'where clause'
    at Query.run (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\dialects\mysql\query.js:52:25)
    at C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async MySQLQueryInterface.rawSelect (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\dialects\abstract\query-interface.js:434:18)
    at async foods.aggregate (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1277:19)
    at async foods.count (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1306:20)
    at async Promise.all (index 0)
    at async foods.findAndCountAll (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1322:27)
    at async Location.paginate (C:\Users\<USER>\Desktop\H5地图规划\后端\app\model\foods.js:31:20)
    at async FoodsController.list (C:\Users\<USER>\Desktop\H5地图规划\后端\app\controller\foods.js:74:13)
name: "SequelizeDatabaseError"
parent: {"code":"ER_BAD_FIELD_ERROR","errno":1054,"sqlState":"42S22","sqlMessage":"Unknown column 'foods.code' in 'where clause'","sql":"SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO';"}
original: {"code":"ER_BAD_FIELD_ERROR","errno":1054,"sqlState":"42S22","sqlMessage":"Unknown column 'foods.code' in 'where clause'","sql":"SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO';"}
sql: "SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO';"
parameters: {}
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 30224
hostname: l

2025-08-21 23:56:32,852 INFO 30224 [-/127.0.0.1/-/5ms POST /foods/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO' ORDER BY `foods`.`id` DESC LIMIT 0, 999;
2025-08-21 23:56:58,818 INFO 30224 [-/127.0.0.1/-/4ms POST /paths/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `paths` AS `paths` WHERE `paths`.`code` = 'PATH_MDSM0MKR_0PEGO' AND `paths`.`uid` = 1;
2025-08-21 23:56:58,818 INFO 30224 [-/127.0.0.1/-/4ms POST /paths/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `code`, `title`, `jsons`, `time`, `uid` FROM `paths` AS `paths` WHERE `paths`.`code` = 'PATH_MDSM0MKR_0PEGO' AND `paths`.`uid` = 1 ORDER BY `paths`.`id` DESC LIMIT 0, 1;
2025-08-21 23:56:58,846 INFO 30224 [-/127.0.0.1/-/3ms POST /foods/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO' ORDER BY `foods`.`id` DESC LIMIT 0, 999;
2025-08-21 23:56:58,848 ERROR 30224 [-/127.0.0.1/-/4ms POST /foods/list] nodejs.SequelizeDatabaseError: Unknown column 'foods.code' in 'where clause'
    at Query.run (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\dialects\mysql\query.js:52:25)
    at C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async MySQLQueryInterface.select (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\dialects\abstract\query-interface.js:407:12)
    at async foods.findAll (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1140:21)
    at async Promise.all (index 1)
    at async foods.findAndCountAll (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1322:27)
    at async Location.paginate (C:\Users\<USER>\Desktop\H5地图规划\后端\app\model\foods.js:31:20)
    at async FoodsController.list (C:\Users\<USER>\Desktop\H5地图规划\后端\app\controller\foods.js:74:13)
    at async C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-development\app\middleware\egg_loader_trace.js:7:50
name: "SequelizeDatabaseError"
parent: {"code":"ER_BAD_FIELD_ERROR","errno":1054,"sqlState":"42S22","sqlMessage":"Unknown column 'foods.code' in 'where clause'","sql":"SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO' ORDER BY `foods`.`id` DESC LIMIT 0, 999;"}
original: {"code":"ER_BAD_FIELD_ERROR","errno":1054,"sqlState":"42S22","sqlMessage":"Unknown column 'foods.code' in 'where clause'","sql":"SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO' ORDER BY `foods`.`id` DESC LIMIT 0, 999;"}
sql: "SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO' ORDER BY `foods`.`id` DESC LIMIT 0, 999;"
parameters: {}
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 30224
hostname: l

2025-08-21 23:56:58,849 INFO 30224 [-/127.0.0.1/-/6ms POST /foods/list] [egg-sequelize](5ms) Executed (default): SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO';
2025-08-21 23:57:16,660 INFO 30224 [-/127.0.0.1/-/5ms POST /paths/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `paths` AS `paths` WHERE `paths`.`code` = 'PATH_MDSM0MKR_0PEGO' AND `paths`.`uid` = 1;
2025-08-21 23:57:16,660 INFO 30224 [-/127.0.0.1/-/5ms POST /paths/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `code`, `title`, `jsons`, `time`, `uid` FROM `paths` AS `paths` WHERE `paths`.`code` = 'PATH_MDSM0MKR_0PEGO' AND `paths`.`uid` = 1 ORDER BY `paths`.`id` DESC LIMIT 0, 1;
2025-08-21 23:57:16,726 INFO 30224 [-/127.0.0.1/-/4ms POST /foods/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO';
2025-08-21 23:57:16,728 ERROR 30224 [-/127.0.0.1/-/5ms POST /foods/list] nodejs.SequelizeDatabaseError: Unknown column 'foods.code' in 'where clause'
    at Query.run (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\dialects\mysql\query.js:52:25)
    at C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async MySQLQueryInterface.rawSelect (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\dialects\abstract\query-interface.js:434:18)
    at async foods.aggregate (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1277:19)
    at async foods.count (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1306:20)
    at async Promise.all (index 0)
    at async foods.findAndCountAll (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1322:27)
    at async Location.paginate (C:\Users\<USER>\Desktop\H5地图规划\后端\app\model\foods.js:31:20)
    at async FoodsController.list (C:\Users\<USER>\Desktop\H5地图规划\后端\app\controller\foods.js:74:13)
name: "SequelizeDatabaseError"
parent: {"code":"ER_BAD_FIELD_ERROR","errno":1054,"sqlState":"42S22","sqlMessage":"Unknown column 'foods.code' in 'where clause'","sql":"SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO';"}
original: {"code":"ER_BAD_FIELD_ERROR","errno":1054,"sqlState":"42S22","sqlMessage":"Unknown column 'foods.code' in 'where clause'","sql":"SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO';"}
sql: "SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO';"
parameters: {}
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 30224
hostname: l

2025-08-21 23:57:16,729 INFO 30224 [-/127.0.0.1/-/7ms POST /foods/list] [egg-sequelize](5ms) Executed (default): SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO' ORDER BY `foods`.`id` DESC LIMIT 0, 999;
2025-08-21 23:57:23,236 INFO 30224 [-/127.0.0.1/-/1ms POST /paths/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `paths` AS `paths` WHERE `paths`.`code` = 'PATH_MDSM0MKR_0PEGO' AND `paths`.`uid` = 1;
2025-08-21 23:57:23,237 INFO 30224 [-/127.0.0.1/-/2ms POST /paths/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `code`, `title`, `jsons`, `time`, `uid` FROM `paths` AS `paths` WHERE `paths`.`code` = 'PATH_MDSM0MKR_0PEGO' AND `paths`.`uid` = 1 ORDER BY `paths`.`id` DESC LIMIT 0, 1;
2025-08-21 23:57:23,294 INFO 30224 [-/127.0.0.1/-/1ms POST /foods/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`pid` = 1;
2025-08-21 23:57:23,295 INFO 30224 [-/127.0.0.1/-/2ms POST /foods/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `foods` AS `foods` WHERE `foods`.`pid` = 1 ORDER BY `foods`.`id` DESC LIMIT 0, 999;
2025-08-21 23:57:23,355 INFO 30224 [-/127.0.0.1/-/4ms POST /hotels/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `hotels` AS `hotels` WHERE `hotels`.`code` = 'PATH_MDSM0MKR_0PEGO';
2025-08-21 23:57:23,357 ERROR 30224 [-/127.0.0.1/-/4ms POST /hotels/list] nodejs.SequelizeDatabaseError: Unknown column 'hotels.code' in 'where clause'
    at Query.run (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\dialects\mysql\query.js:52:25)
    at C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async MySQLQueryInterface.rawSelect (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\dialects\abstract\query-interface.js:434:18)
    at async hotels.aggregate (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1277:19)
    at async hotels.count (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1306:20)
    at async Promise.all (index 0)
    at async hotels.findAndCountAll (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1322:27)
    at async Location.paginate (C:\Users\<USER>\Desktop\H5地图规划\后端\app\model\hotels.js:31:20)
    at async HotelsController.list (C:\Users\<USER>\Desktop\H5地图规划\后端\app\controller\hotels.js:74:13)
name: "SequelizeDatabaseError"
parent: {"code":"ER_BAD_FIELD_ERROR","errno":1054,"sqlState":"42S22","sqlMessage":"Unknown column 'hotels.code' in 'where clause'","sql":"SELECT count(*) AS `count` FROM `hotels` AS `hotels` WHERE `hotels`.`code` = 'PATH_MDSM0MKR_0PEGO';"}
original: {"code":"ER_BAD_FIELD_ERROR","errno":1054,"sqlState":"42S22","sqlMessage":"Unknown column 'hotels.code' in 'where clause'","sql":"SELECT count(*) AS `count` FROM `hotels` AS `hotels` WHERE `hotels`.`code` = 'PATH_MDSM0MKR_0PEGO';"}
sql: "SELECT count(*) AS `count` FROM `hotels` AS `hotels` WHERE `hotels`.`code` = 'PATH_MDSM0MKR_0PEGO';"
parameters: {}
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 30224
hostname: l

2025-08-21 23:57:23,357 INFO 30224 [-/127.0.0.1/-/6ms POST /hotels/list] [egg-sequelize](5ms) Executed (default): SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `hotels` AS `hotels` WHERE `hotels`.`code` = 'PATH_MDSM0MKR_0PEGO' ORDER BY `hotels`.`id` DESC LIMIT 0, 999;
2025-08-21 23:57:26,795 INFO 30224 [-/127.0.0.1/-/2ms POST /paths/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `paths` AS `paths` WHERE `paths`.`code` = 'PATH_MDSM0MKR_0PEGO' AND `paths`.`uid` = 1;
2025-08-21 23:57:26,795 INFO 30224 [-/127.0.0.1/-/2ms POST /paths/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `code`, `title`, `jsons`, `time`, `uid` FROM `paths` AS `paths` WHERE `paths`.`code` = 'PATH_MDSM0MKR_0PEGO' AND `paths`.`uid` = 1 ORDER BY `paths`.`id` DESC LIMIT 0, 1;
2025-08-21 23:57:26,848 INFO 30224 [-/127.0.0.1/-/1ms POST /foods/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`pid` = 1;
2025-08-21 23:57:26,848 INFO 30224 [-/127.0.0.1/-/1ms POST /foods/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `foods` AS `foods` WHERE `foods`.`pid` = 1 ORDER BY `foods`.`id` DESC LIMIT 0, 999;
2025-08-21 23:57:26,902 INFO 30224 [-/127.0.0.1/-/1ms POST /hotels/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `hotels` AS `hotels` WHERE `hotels`.`pid` = 1;
2025-08-21 23:57:26,902 INFO 30224 [-/127.0.0.1/-/1ms POST /hotels/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `hotels` AS `hotels` WHERE `hotels`.`pid` = 1 ORDER BY `hotels`.`id` DESC LIMIT 0, 999;
2025-08-21 23:57:28,882 INFO 30224 [-/127.0.0.1/-/3ms POST /paths/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `paths` AS `paths` WHERE `paths`.`code` = 'PATH_MDSM0MKR_0PEGO' AND `paths`.`uid` = 1;
2025-08-21 23:57:28,882 INFO 30224 [-/127.0.0.1/-/3ms POST /paths/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `code`, `title`, `jsons`, `time`, `uid` FROM `paths` AS `paths` WHERE `paths`.`code` = 'PATH_MDSM0MKR_0PEGO' AND `paths`.`uid` = 1 ORDER BY `paths`.`id` DESC LIMIT 0, 1;
2025-08-21 23:57:28,932 INFO 30224 [-/127.0.0.1/-/1ms POST /foods/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`pid` = 1;
2025-08-21 23:57:28,933 INFO 30224 [-/127.0.0.1/-/2ms POST /foods/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `foods` AS `foods` WHERE `foods`.`pid` = 1 ORDER BY `foods`.`id` DESC LIMIT 0, 999;
2025-08-21 23:57:28,998 INFO 30224 [-/127.0.0.1/-/2ms POST /hotels/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `hotels` AS `hotels` WHERE `hotels`.`pid` = 1;
2025-08-21 23:57:28,998 INFO 30224 [-/127.0.0.1/-/2ms POST /hotels/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `hotels` AS `hotels` WHERE `hotels`.`pid` = 1 ORDER BY `hotels`.`id` DESC LIMIT 0, 999;
2025-08-21 23:58:24,246 INFO 30224 [-/127.0.0.1/-/5ms POST /paths/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `code`, `title`, `jsons`, `time`, `uid` FROM `paths` AS `paths` WHERE `paths`.`code` = 'PATH_MDSM0MKR_0PEGO' AND `paths`.`uid` = 1 ORDER BY `paths`.`id` DESC LIMIT 0, 1;
2025-08-21 23:58:24,247 INFO 30224 [-/127.0.0.1/-/6ms POST /paths/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `paths` AS `paths` WHERE `paths`.`code` = 'PATH_MDSM0MKR_0PEGO' AND `paths`.`uid` = 1;
2025-08-21 23:58:24,317 INFO 30224 [-/127.0.0.1/-/3ms POST /foods/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `foods` AS `foods` WHERE `foods`.`pid` = 1 ORDER BY `foods`.`id` DESC LIMIT 0, 999;
2025-08-21 23:58:24,318 INFO 30224 [-/127.0.0.1/-/4ms POST /foods/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`pid` = 1;
2025-08-21 23:58:24,342 INFO 30224 [-/127.0.0.1/-/4ms POST /hotels/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `hotels` AS `hotels` WHERE `hotels`.`pid` = 1 ORDER BY `hotels`.`id` DESC LIMIT 0, 999;
2025-08-21 23:58:24,342 INFO 30224 [-/127.0.0.1/-/4ms POST /hotels/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `hotels` AS `hotels` WHERE `hotels`.`pid` = 1;

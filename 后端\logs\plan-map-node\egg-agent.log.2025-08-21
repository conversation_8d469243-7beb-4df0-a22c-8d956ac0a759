2025-08-04 19:59:13,092 INFO 10424 [egg-sequelize](0ms) Executed (default): SELECT 1+1 AS result
2025-08-04 19:59:12,748 INFO 10424 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\logs\\plan-map-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"plan-map-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-08-04 19:59:13,033 INFO 10424 [egg:core] dump config after load, 3ms
2025-08-04 19:59:13,066 INFO 10424 [egg-watcher] Start watching: ["C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app","C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\config","C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\mocks","C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\mocks_proxy","C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app.js"]
2025-08-04 19:59:13,067 INFO 10424 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app"
2025-08-04 19:59:13,067 INFO 10424 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\config"
2025-08-04 19:59:13,067 INFO 10424 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\mocks"
2025-08-04 19:59:13,067 INFO 10424 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\mocks_proxy"
2025-08-04 19:59:13,067 INFO 10424 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app.js"
2025-08-04 19:59:13,068 INFO 10424 [egg-watcher:agent] watcher start success
2025-08-04 19:59:13,100 INFO 10424 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [381ms] - #0 Process Start
                      ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [476ms] - #1 Application Start
                      ▇ [8ms] - #2 Load Plugin
                       ▇ [12ms] - #3 Load Config
                       ▇ [0ms] - #4 Require(0) config/config.default.js
                       ▇ [0ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                       ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                       ▇ [0ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                       ▇ [0ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                       ▇ [0ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                       ▇ [0ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                       ▇ [0ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                       ▇ [0ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                       ▇ [0ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                       ▇ [0ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                       ▇ [0ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                       ▇ [0ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                       ▇ [0ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                       ▇ [1ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                       ▇ [1ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                       ▇ [0ms] - #20 Require(16) node_modules/egg/config/config.default.js
                       ▇ [0ms] - #21 Require(17) config/config.default.js
                       ▇ [0ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                       ▇ [0ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                       ▇ [0ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                       ▇ [0ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                       ▇ [0ms] - #26 Require(22) node_modules/egg/config/config.local.js
                       ▇▇ [45ms] - #27 Load extend/agent.js
                       ▇ [1ms] - #28 Require(23) node_modules/egg-security/app/extend/agent.js
                        ▇▇ [39ms] - #29 Require(24) node_modules/egg-schedule/app/extend/agent.js
                          ▇ [1ms] - #30 Require(25) node_modules/egg-logrotator/app/extend/agent.js
                          ▇ [33ms] - #31 Load extend/context.js
                          ▇ [9ms] - #32 Require(26) node_modules/egg-security/app/extend/context.js
                           ▇ [2ms] - #33 Require(27) node_modules/egg-jsonp/app/extend/context.js
                           ▇ [0ms] - #34 Require(28) node_modules/egg-i18n/app/extend/context.js
                           ▇ [15ms] - #35 Require(29) node_modules/egg-multipart/app/extend/context.js
                            ▇ [1ms] - #36 Require(30) node_modules/egg-view/app/extend/context.js
                            ▇ [1ms] - #37 Require(31) node_modules/egg/app/extend/context.js
                            ▇ [17ms] - #38 Load agent.js
                            ▇ [0ms] - #39 Require(32) node_modules/egg-security/agent.js
                            ▇ [0ms] - #40 Require(33) node_modules/egg-onerror/agent.js
                            ▇ [6ms] - #41 Require(34) node_modules/egg-watcher/agent.js
                            ▇ [1ms] - #42 Require(35) node_modules/egg-schedule/agent.js
                            ▇ [8ms] - #43 Require(36) node_modules/egg-development/agent.js
                             ▇ [0ms] - #44 Require(37) node_modules/egg-logrotator/agent.js
                             ▇ [0ms] - #45 Require(38) node_modules/egg-sequelize/agent.js
                             ▇ [0ms] - #46 Require(39) node_modules/egg/agent.js
                             ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [318ms] - #47 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                             ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [299ms] - #48 Before Start in node_modules/egg-schedule/agent.js:12:9
                             ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [346ms] - #49 Before Start in node_modules/egg-development/agent.js:9:9
                                             ▇ [11ms] - #50 Load "Symbol(model)" to Application
                                             ▇▇▇ [63ms] - #51 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
2025-08-04 19:59:13,100 INFO 10424 [egg:core] dump config after ready, 3ms
2025-08-04 19:59:40,264 WARN 10424 [agent:development] reload worker because C:\Users\<USER>\Desktop\H5地图规划\后端\config\config.default.js change
2025-08-04 19:59:40,051 INFO 10424 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\config\\config.default.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":36310271996476430,"size":1652,"blocks":8,"atimeMs":1754308780049.0837,"mtimeMs":1754308780049.0837,"ctimeMs":1754308780049.0837,"birthtimeMs":1753873201698.53,"atime":"2025-08-04T11:59:40.049Z","mtime":"2025-08-04T11:59:40.049Z","ctime":"2025-08-04T11:59:40.049Z","birthtime":"2025-07-30T11:00:01.699Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-04 19:59:40,052 INFO 10424 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\config\\config.default.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":36310271996476430,"size":1652,"blocks":8,"atimeMs":1754308780049.0837,"mtimeMs":1754308780049.0837,"ctimeMs":1754308780049.0837,"birthtimeMs":1753873201698.53,"atime":"2025-08-04T11:59:40.049Z","mtime":"2025-08-04T11:59:40.049Z","ctime":"2025-08-04T11:59:40.049Z","birthtime":"2025-07-30T11:00:01.699Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-04 19:59:40,052 INFO 10424 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\config\\config.default.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":36310271996476430,"size":1652,"blocks":8,"atimeMs":1754308780049.0837,"mtimeMs":1754308780049.0837,"ctimeMs":1754308780049.0837,"birthtimeMs":1753873201698.53,"atime":"2025-08-04T11:59:40.049Z","mtime":"2025-08-04T11:59:40.049Z","ctime":"2025-08-04T11:59:40.049Z","birthtime":"2025-07-30T11:00:01.699Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-04 19:59:41,206 WARN 10424 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-04 19:59:48,231 WARN 10424 [agent:development] reload worker because C:\Users\<USER>\Desktop\H5地图规划\后端\config\config.default.js change
2025-08-04 19:59:48,028 INFO 10424 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\config\\config.default.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":36310271996476430,"size":1649,"blocks":8,"atimeMs":1754308788027.4788,"mtimeMs":1754308788027.4788,"ctimeMs":1754308788027.4788,"birthtimeMs":1753873201698.53,"atime":"2025-08-04T11:59:48.027Z","mtime":"2025-08-04T11:59:48.027Z","ctime":"2025-08-04T11:59:48.027Z","birthtime":"2025-07-30T11:00:01.699Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-04 19:59:48,029 INFO 10424 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\config\\config.default.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":36310271996476430,"size":1649,"blocks":8,"atimeMs":1754308788027.4788,"mtimeMs":1754308788027.4788,"ctimeMs":1754308788027.4788,"birthtimeMs":1753873201698.53,"atime":"2025-08-04T11:59:48.027Z","mtime":"2025-08-04T11:59:48.027Z","ctime":"2025-08-04T11:59:48.027Z","birthtime":"2025-07-30T11:00:01.699Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-04 19:59:48,029 INFO 10424 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\config\\config.default.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":36310271996476430,"size":1649,"blocks":8,"atimeMs":1754308788027.4788,"mtimeMs":1754308788027.4788,"ctimeMs":1754308788027.4788,"birthtimeMs":1753873201698.53,"atime":"2025-08-04T11:59:48.027Z","mtime":"2025-08-04T11:59:48.027Z","ctime":"2025-08-04T11:59:48.027Z","birthtime":"2025-07-30T11:00:01.699Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-04 19:59:49,198 WARN 10424 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-04 20:00:53,123 WARN 10424 [agent:development] reload worker because C:\Users\<USER>\Desktop\H5地图规划\后端\config\config.default.js change
2025-08-04 20:00:52,908 INFO 10424 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\config\\config.default.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":36310271996476430,"size":1649,"blocks":8,"atimeMs":1754308852907.5176,"mtimeMs":1754308852907.5176,"ctimeMs":1754308852907.5176,"birthtimeMs":1753873201698.53,"atime":"2025-08-04T12:00:52.908Z","mtime":"2025-08-04T12:00:52.908Z","ctime":"2025-08-04T12:00:52.908Z","birthtime":"2025-07-30T11:00:01.699Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-04 20:00:52,909 INFO 10424 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\config\\config.default.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":36310271996476430,"size":1649,"blocks":8,"atimeMs":1754308852907.5176,"mtimeMs":1754308852907.5176,"ctimeMs":1754308852907.5176,"birthtimeMs":1753873201698.53,"atime":"2025-08-04T12:00:52.908Z","mtime":"2025-08-04T12:00:52.908Z","ctime":"2025-08-04T12:00:52.908Z","birthtime":"2025-07-30T11:00:01.699Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-04 20:00:54,109 WARN 10424 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-04 20:01:00,722 INFO 23340 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-04 20:01:00,482 INFO 23340 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\logs\\plan-map-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"plan-map-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-08-04 20:01:00,672 INFO 23340 [egg:core] dump config after load, 3ms
2025-08-04 20:01:00,699 INFO 23340 [egg-watcher] Start watching: ["C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app","C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\config","C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\mocks","C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\mocks_proxy","C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app.js"]
2025-08-04 20:01:00,700 INFO 23340 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app"
2025-08-04 20:01:00,700 INFO 23340 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\config"
2025-08-04 20:01:00,700 INFO 23340 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\mocks"
2025-08-04 20:01:00,700 INFO 23340 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\mocks_proxy"
2025-08-04 20:01:00,700 INFO 23340 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app.js"
2025-08-04 20:01:00,701 INFO 23340 [egg-watcher:agent] watcher start success
2025-08-04 20:01:00,728 INFO 23340 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [367ms] - #0 Process Start
                          ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [331ms] - #1 Application Start
                          ▇ [8ms] - #2 Load Plugin
                           ▇ [11ms] - #3 Load Config
                           ▇ [0ms] - #4 Require(0) config/config.default.js
                           ▇ [0ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                           ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                           ▇ [0ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                           ▇ [1ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                           ▇ [0ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                           ▇ [1ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                           ▇ [1ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                           ▇ [0ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                           ▇ [0ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                           ▇ [0ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                           ▇ [0ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                           ▇ [1ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                           ▇ [0ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                           ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                           ▇ [1ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                           ▇ [0ms] - #20 Require(16) node_modules/egg/config/config.default.js
                            ▇ [0ms] - #21 Require(17) config/config.default.js
                            ▇ [0ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                            ▇ [1ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                            ▇ [0ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                            ▇ [0ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                            ▇ [0ms] - #26 Require(22) node_modules/egg/config/config.local.js
                            ▇ [28ms] - #27 Load extend/agent.js
                            ▇ [1ms] - #28 Require(23) node_modules/egg-security/app/extend/agent.js
                            ▇ [25ms] - #29 Require(24) node_modules/egg-schedule/app/extend/agent.js
                              ▇ [1ms] - #30 Require(25) node_modules/egg-logrotator/app/extend/agent.js
                              ▇ [19ms] - #31 Load extend/context.js
                              ▇ [4ms] - #32 Require(26) node_modules/egg-security/app/extend/context.js
                              ▇ [1ms] - #33 Require(27) node_modules/egg-jsonp/app/extend/context.js
                              ▇ [0ms] - #34 Require(28) node_modules/egg-i18n/app/extend/context.js
                              ▇ [8ms] - #35 Require(29) node_modules/egg-multipart/app/extend/context.js
                               ▇ [0ms] - #36 Require(30) node_modules/egg-view/app/extend/context.js
                               ▇ [0ms] - #37 Require(31) node_modules/egg/app/extend/context.js
                               ▇ [11ms] - #38 Load agent.js
                               ▇ [0ms] - #39 Require(32) node_modules/egg-security/agent.js
                               ▇ [0ms] - #40 Require(33) node_modules/egg-onerror/agent.js
                               ▇ [3ms] - #41 Require(34) node_modules/egg-watcher/agent.js
                               ▇ [1ms] - #42 Require(35) node_modules/egg-schedule/agent.js
                               ▇ [4ms] - #43 Require(36) node_modules/egg-development/agent.js
                                ▇ [1ms] - #44 Require(37) node_modules/egg-logrotator/agent.js
                                ▇ [0ms] - #45 Require(38) node_modules/egg-sequelize/agent.js
                                ▇ [0ms] - #46 Require(39) node_modules/egg/agent.js
                                ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [216ms] - #47 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                ▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [201ms] - #48 Before Start in node_modules/egg-schedule/agent.js:12:9
                                ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [239ms] - #49 Before Start in node_modules/egg-development/agent.js:9:9
                                             ▇ [11ms] - #50 Load "Symbol(model)" to Application
                                             ▇▇▇ [54ms] - #51 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
2025-08-04 20:01:00,728 INFO 23340 [egg:core] dump config after ready, 3ms
2025-08-04 20:01:29,498 WARN 23340 [agent:development] reload worker because C:\Users\<USER>\Desktop\H5地图规划\后端\config\config.default.js change
2025-08-04 20:01:29,294 INFO 23340 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\config\\config.default.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":36310271996476430,"size":1696,"blocks":8,"atimeMs":1754308889292.3584,"mtimeMs":1754308889292.3584,"ctimeMs":1754308889292.3584,"birthtimeMs":1753873201698.53,"atime":"2025-08-04T12:01:29.292Z","mtime":"2025-08-04T12:01:29.292Z","ctime":"2025-08-04T12:01:29.292Z","birthtime":"2025-07-30T11:00:01.699Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-04 20:01:29,295 INFO 23340 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\config\\config.default.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":36310271996476430,"size":1696,"blocks":8,"atimeMs":1754308889292.3584,"mtimeMs":1754308889292.3584,"ctimeMs":1754308889292.3584,"birthtimeMs":1753873201698.53,"atime":"2025-08-04T12:01:29.292Z","mtime":"2025-08-04T12:01:29.292Z","ctime":"2025-08-04T12:01:29.292Z","birthtime":"2025-07-30T11:00:01.699Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-04 20:01:29,296 INFO 23340 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\config\\config.default.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":36310271996476430,"size":1696,"blocks":8,"atimeMs":1754308889292.3584,"mtimeMs":1754308889292.3584,"ctimeMs":1754308889292.3584,"birthtimeMs":1753873201698.53,"atime":"2025-08-04T12:01:29.292Z","mtime":"2025-08-04T12:01:29.292Z","ctime":"2025-08-04T12:01:29.292Z","birthtime":"2025-07-30T11:00:01.699Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-04 20:01:30,419 WARN 23340 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-04 20:01:40,714 WARN 23340 [agent:development] reload worker because C:\Users\<USER>\Desktop\H5地图规划\后端\config\config.default.js change
2025-08-04 20:01:40,507 INFO 23340 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\config\\config.default.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":36310271996476430,"size":1696,"blocks":8,"atimeMs":1754308900506.7644,"mtimeMs":1754308900506.7644,"ctimeMs":1754308900506.7644,"birthtimeMs":1753873201698.53,"atime":"2025-08-04T12:01:40.507Z","mtime":"2025-08-04T12:01:40.507Z","ctime":"2025-08-04T12:01:40.507Z","birthtime":"2025-07-30T11:00:01.699Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-04 20:01:40,508 INFO 23340 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\config\\config.default.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":36310271996476430,"size":1696,"blocks":8,"atimeMs":1754308900506.7644,"mtimeMs":1754308900506.7644,"ctimeMs":1754308900506.7644,"birthtimeMs":1753873201698.53,"atime":"2025-08-04T12:01:40.507Z","mtime":"2025-08-04T12:01:40.507Z","ctime":"2025-08-04T12:01:40.507Z","birthtime":"2025-07-30T11:00:01.699Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-04 20:01:40,508 INFO 23340 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\config\\config.default.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":36310271996476430,"size":1696,"blocks":8,"atimeMs":1754308900506.7644,"mtimeMs":1754308900506.7644,"ctimeMs":1754308900506.7644,"birthtimeMs":1753873201698.53,"atime":"2025-08-04T12:01:40.507Z","mtime":"2025-08-04T12:01:40.507Z","ctime":"2025-08-04T12:01:40.507Z","birthtime":"2025-07-30T11:00:01.699Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-04 20:01:41,664 WARN 23340 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-04 20:01:55,121 INFO 18124 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-04 20:01:54,875 INFO 18124 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\logs\\plan-map-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"plan-map-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-08-04 20:01:55,070 INFO 18124 [egg:core] dump config after load, 3ms
2025-08-04 20:01:55,098 INFO 18124 [egg-watcher] Start watching: ["C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app","C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\config","C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\mocks","C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\mocks_proxy","C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app.js"]
2025-08-04 20:01:55,098 INFO 18124 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app"
2025-08-04 20:01:55,098 INFO 18124 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\config"
2025-08-04 20:01:55,098 INFO 18124 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\mocks"
2025-08-04 20:01:55,099 INFO 18124 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\mocks_proxy"
2025-08-04 20:01:55,099 INFO 18124 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app.js"
2025-08-04 20:01:55,099 INFO 18124 [egg-watcher:agent] watcher start success
2025-08-04 20:01:55,127 INFO 18124 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [380ms] - #0 Process Start
                          ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [340ms] - #1 Application Start
                          ▇ [8ms] - #2 Load Plugin
                           ▇ [12ms] - #3 Load Config
                           ▇ [0ms] - #4 Require(0) config/config.default.js
                           ▇ [0ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                           ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                           ▇ [0ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                           ▇ [0ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                           ▇ [0ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                           ▇ [0ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                           ▇ [0ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                           ▇ [0ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                           ▇ [1ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                           ▇ [0ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                           ▇ [0ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                           ▇ [0ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                           ▇ [0ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                           ▇ [1ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                           ▇ [0ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                           ▇ [1ms] - #20 Require(16) node_modules/egg/config/config.default.js
                            ▇ [0ms] - #21 Require(17) config/config.default.js
                            ▇ [0ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                            ▇ [0ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                            ▇ [0ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                            ▇ [0ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                            ▇ [0ms] - #26 Require(22) node_modules/egg/config/config.local.js
                            ▇▇ [30ms] - #27 Load extend/agent.js
                            ▇ [1ms] - #28 Require(23) node_modules/egg-security/app/extend/agent.js
                            ▇ [26ms] - #29 Require(24) node_modules/egg-schedule/app/extend/agent.js
                              ▇ [0ms] - #30 Require(25) node_modules/egg-logrotator/app/extend/agent.js
                              ▇ [19ms] - #31 Load extend/context.js
                              ▇ [5ms] - #32 Require(26) node_modules/egg-security/app/extend/context.js
                              ▇ [1ms] - #33 Require(27) node_modules/egg-jsonp/app/extend/context.js
                              ▇ [1ms] - #34 Require(28) node_modules/egg-i18n/app/extend/context.js
                              ▇ [8ms] - #35 Require(29) node_modules/egg-multipart/app/extend/context.js
                               ▇ [1ms] - #36 Require(30) node_modules/egg-view/app/extend/context.js
                               ▇ [2ms] - #37 Require(31) node_modules/egg/app/extend/context.js
                               ▇ [12ms] - #38 Load agent.js
                               ▇ [1ms] - #39 Require(32) node_modules/egg-security/agent.js
                               ▇ [0ms] - #40 Require(33) node_modules/egg-onerror/agent.js
                               ▇ [4ms] - #41 Require(34) node_modules/egg-watcher/agent.js
                               ▇ [1ms] - #42 Require(35) node_modules/egg-schedule/agent.js
                                ▇ [4ms] - #43 Require(36) node_modules/egg-development/agent.js
                                ▇ [0ms] - #44 Require(37) node_modules/egg-logrotator/agent.js
                                ▇ [0ms] - #45 Require(38) node_modules/egg-sequelize/agent.js
                                ▇ [1ms] - #46 Require(39) node_modules/egg/agent.js
                                ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [222ms] - #47 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                ▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [209ms] - #48 Before Start in node_modules/egg-schedule/agent.js:12:9
                                ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [246ms] - #49 Before Start in node_modules/egg-development/agent.js:9:9
                                             ▇ [11ms] - #50 Load "Symbol(model)" to Application
                                             ▇▇▇ [56ms] - #51 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
2025-08-04 20:01:55,127 INFO 18124 [egg:core] dump config after ready, 3ms
2025-08-04 20:02:24,908 INFO 18124 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\config\\config.default.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":36310271996476430,"size":1696,"blocks":8,"atimeMs":1754308944906.1602,"mtimeMs":1754308944906.1602,"ctimeMs":1754308944906.1602,"birthtimeMs":1753873201698.53,"atime":"2025-08-04T12:02:24.906Z","mtime":"2025-08-04T12:02:24.906Z","ctime":"2025-08-04T12:02:24.906Z","birthtime":"2025-07-30T11:00:01.699Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-04 20:02:24,909 INFO 18124 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\config\\config.default.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":36310271996476430,"size":1696,"blocks":8,"atimeMs":1754308944906.1602,"mtimeMs":1754308944906.1602,"ctimeMs":1754308944906.1602,"birthtimeMs":1753873201698.53,"atime":"2025-08-04T12:02:24.906Z","mtime":"2025-08-04T12:02:24.906Z","ctime":"2025-08-04T12:02:24.906Z","birthtime":"2025-07-30T11:00:01.699Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-04 20:02:24,910 INFO 18124 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\config\\config.default.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":36310271996476430,"size":1696,"blocks":8,"atimeMs":1754308944906.1602,"mtimeMs":1754308944906.1602,"ctimeMs":1754308944906.1602,"birthtimeMs":1753873201698.53,"atime":"2025-08-04T12:02:24.906Z","mtime":"2025-08-04T12:02:24.906Z","ctime":"2025-08-04T12:02:24.906Z","birthtime":"2025-07-30T11:00:01.699Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-04 20:02:25,120 WARN 18124 [agent:development] reload worker because C:\Users\<USER>\Desktop\H5地图规划\后端\config\config.default.js change
2025-08-04 20:02:26,091 WARN 18124 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-21 23:37:45,317 INFO 30096 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-21 23:37:44,929 INFO 30096 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\logs\\plan-map-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"plan-map-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-08-21 23:37:45,254 INFO 30096 [egg:core] dump config after load, 4ms
2025-08-21 23:37:45,290 INFO 30096 [egg-watcher] Start watching: ["C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app","C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\config","C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\mocks","C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\mocks_proxy","C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app.js"]
2025-08-21 23:37:45,290 INFO 30096 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app"
2025-08-21 23:37:45,291 INFO 30096 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\config"
2025-08-21 23:37:45,291 INFO 30096 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\mocks"
2025-08-21 23:37:45,291 INFO 30096 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\mocks_proxy"
2025-08-21 23:37:45,291 INFO 30096 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app.js"
2025-08-21 23:37:45,291 INFO 30096 [egg-watcher:agent] watcher start success
2025-08-21 23:37:45,322 INFO 30096 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [390ms] - #0 Process Start
                     ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [526ms] - #1 Application Start
                     ▇ [10ms] - #2 Load Plugin
                      ▇ [12ms] - #3 Load Config
                      ▇ [0ms] - #4 Require(0) config/config.default.js
                      ▇ [0ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                      ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                      ▇ [1ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                      ▇ [0ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                      ▇ [0ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                      ▇ [0ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                      ▇ [0ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                      ▇ [0ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                      ▇ [0ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                      ▇ [1ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                      ▇ [0ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                      ▇ [1ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                      ▇ [0ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                      ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                      ▇ [0ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                      ▇ [0ms] - #20 Require(16) node_modules/egg/config/config.default.js
                      ▇ [0ms] - #21 Require(17) config/config.default.js
                      ▇ [0ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                      ▇ [0ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                      ▇ [0ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                      ▇ [0ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                      ▇ [0ms] - #26 Require(22) node_modules/egg/config/config.local.js
                      ▇▇ [49ms] - #27 Load extend/agent.js
                      ▇ [1ms] - #28 Require(23) node_modules/egg-security/app/extend/agent.js
                       ▇▇ [41ms] - #29 Require(24) node_modules/egg-schedule/app/extend/agent.js
                         ▇ [1ms] - #30 Require(25) node_modules/egg-logrotator/app/extend/agent.js
                         ▇ [36ms] - #31 Load extend/context.js
                         ▇ [11ms] - #32 Require(26) node_modules/egg-security/app/extend/context.js
                          ▇ [2ms] - #33 Require(27) node_modules/egg-jsonp/app/extend/context.js
                          ▇ [0ms] - #34 Require(28) node_modules/egg-i18n/app/extend/context.js
                          ▇ [17ms] - #35 Require(29) node_modules/egg-multipart/app/extend/context.js
                           ▇ [1ms] - #36 Require(30) node_modules/egg-view/app/extend/context.js
                           ▇ [1ms] - #37 Require(31) node_modules/egg/app/extend/context.js
                           ▇ [20ms] - #38 Load agent.js
                           ▇ [1ms] - #39 Require(32) node_modules/egg-security/agent.js
                           ▇ [0ms] - #40 Require(33) node_modules/egg-onerror/agent.js
                           ▇ [7ms] - #41 Require(34) node_modules/egg-watcher/agent.js
                           ▇ [1ms] - #42 Require(35) node_modules/egg-schedule/agent.js
                           ▇ [9ms] - #43 Require(36) node_modules/egg-development/agent.js
                            ▇ [0ms] - #44 Require(37) node_modules/egg-logrotator/agent.js
                            ▇ [0ms] - #45 Require(38) node_modules/egg-sequelize/agent.js
                            ▇ [0ms] - #46 Require(39) node_modules/egg/agent.js
                            ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [361ms] - #47 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                            ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [341ms] - #48 Before Start in node_modules/egg-schedule/agent.js:12:9
                            ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [386ms] - #49 Before Start in node_modules/egg-development/agent.js:9:9
                                             ▇ [14ms] - #50 Load "Symbol(model)" to Application
                                              ▇▇▇ [68ms] - #51 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
2025-08-21 23:37:45,322 INFO 30096 [egg:core] dump config after ready, 3ms
2025-08-21 23:51:30,020 WARN 30096 [agent:development] reload worker because C:\Users\<USER>\Desktop\H5地图规划\后端\config\plugin.js change
2025-08-21 23:51:29,813 INFO 30096 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app\\controller","stat":{"dev":4026856255,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":25051272928048184,"size":0,"blocks":8,"atimeMs":1755791489808.5715,"mtimeMs":1755787828798.414,"ctimeMs":1755787828798.414,"birthtimeMs":1753873201693.5256,"atime":"2025-08-21T15:51:29.809Z","mtime":"2025-08-21T14:50:28.798Z","ctime":"2025-08-21T14:50:28.798Z","birthtime":"2025-07-30T11:00:01.694Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-21 23:51:29,815 INFO 30096 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app\\controller\\foods.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":27021597765188970,"size":2914,"blocks":8,"atimeMs":1755791489808.5715,"mtimeMs":1755787828797.413,"ctimeMs":1755787828797.413,"birthtimeMs":1755787828797.413,"atime":"2025-08-21T15:51:29.809Z","mtime":"2025-08-21T14:50:28.797Z","ctime":"2025-08-21T14:50:28.797Z","birthtime":"2025-08-21T14:50:28.797Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-21 23:51:29,817 INFO 30096 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app\\model","stat":{"dev":4026856255,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":24488322974627056,"size":0,"blocks":8,"atimeMs":1755791489809.5732,"mtimeMs":1755787828799.4146,"ctimeMs":1755787828799.4146,"birthtimeMs":1753873201694.526,"atime":"2025-08-21T15:51:29.810Z","mtime":"2025-08-21T14:50:28.799Z","ctime":"2025-08-21T14:50:28.799Z","birthtime":"2025-07-30T11:00:01.695Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-21 23:51:29,817 INFO 30096 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app\\model\\foods.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":18577348463874630,"size":964,"blocks":8,"atimeMs":1755791489808.5715,"mtimeMs":1755787828799.4146,"ctimeMs":1755787828799.4146,"birthtimeMs":1755787828798.414,"atime":"2025-08-21T15:51:29.809Z","mtime":"2025-08-21T14:50:28.799Z","ctime":"2025-08-21T14:50:28.799Z","birthtime":"2025-08-21T14:50:28.798Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-21 23:51:29,817 INFO 30096 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app\\controller\\hotels.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":39125021763747710,"size":2921,"blocks":8,"atimeMs":1755791489808.5715,"mtimeMs":1755787828798.414,"ctimeMs":1755787828798.414,"birthtimeMs":1755787828797.413,"atime":"2025-08-21T15:51:29.809Z","mtime":"2025-08-21T14:50:28.798Z","ctime":"2025-08-21T14:50:28.798Z","birthtime":"2025-08-21T14:50:28.797Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-21 23:51:29,817 INFO 30096 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app\\controller\\upload.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":11258999069397228,"size":6720,"blocks":16,"atimeMs":1755791489808.5715,"mtimeMs":1755787828798.414,"ctimeMs":1755787828798.414,"birthtimeMs":1755787828798.414,"atime":"2025-08-21T15:51:29.809Z","mtime":"2025-08-21T14:50:28.798Z","ctime":"2025-08-21T14:50:28.798Z","birthtime":"2025-08-21T14:50:28.798Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-21 23:51:29,817 INFO 30096 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app\\service\\upload.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":33495522229549068,"size":3152,"blocks":8,"atimeMs":1755791489809.5732,"mtimeMs":1755787828800.917,"ctimeMs":1755787828800.917,"birthtimeMs":1755787828800.917,"atime":"2025-08-21T15:51:29.810Z","mtime":"2025-08-21T14:50:28.801Z","ctime":"2025-08-21T14:50:28.801Z","birthtime":"2025-08-21T14:50:28.801Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-21 23:51:29,818 INFO 30096 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app\\router.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":34621422136211092,"size":1981,"blocks":8,"atimeMs":1755791489808.5715,"mtimeMs":1755787828799.4146,"ctimeMs":1755787828799.4146,"birthtimeMs":1753873201696.5283,"atime":"2025-08-21T15:51:29.809Z","mtime":"2025-08-21T14:50:28.799Z","ctime":"2025-08-21T14:50:28.799Z","birthtime":"2025-07-30T11:00:01.697Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-21 23:51:29,818 INFO 30096 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app\\schedule","stat":{"dev":4026856255,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":43910096367662790,"size":0,"blocks":0,"atimeMs":1755791489809.5732,"mtimeMs":1753873201696.5283,"ctimeMs":1753873201696.5283,"birthtimeMs":1753873201696.5283,"atime":"2025-08-21T15:51:29.810Z","mtime":"2025-07-30T11:00:01.697Z","ctime":"2025-07-30T11:00:01.697Z","birthtime":"2025-07-30T11:00:01.697Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-21 23:51:29,818 INFO 30096 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app\\service","stat":{"dev":4026856255,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":21392098230811230,"size":0,"blocks":0,"atimeMs":1755791489811.5752,"mtimeMs":1755787828800.917,"ctimeMs":1755787828800.917,"birthtimeMs":1753873201697.529,"atime":"2025-08-21T15:51:29.812Z","mtime":"2025-08-21T14:50:28.801Z","ctime":"2025-08-21T14:50:28.801Z","birthtime":"2025-07-30T11:00:01.698Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-21 23:51:29,819 INFO 30096 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app\\utils","stat":{"dev":4026856255,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":23362423067786220,"size":0,"blocks":0,"atimeMs":1755791489809.5732,"mtimeMs":1753873201698.53,"ctimeMs":1753873201698.53,"birthtimeMs":1753873201697.529,"atime":"2025-08-21T15:51:29.810Z","mtime":"2025-07-30T11:00:01.699Z","ctime":"2025-07-30T11:00:01.699Z","birthtime":"2025-07-30T11:00:01.698Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-21 23:51:29,819 INFO 30096 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\config\\config.default.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":36591746973187090,"size":2336,"blocks":8,"atimeMs":1755791489809.5732,"mtimeMs":1755787828800.917,"ctimeMs":1755787828800.917,"birthtimeMs":1753873201698.53,"atime":"2025-08-21T15:51:29.810Z","mtime":"2025-08-21T14:50:28.801Z","ctime":"2025-08-21T14:50:28.801Z","birthtime":"2025-07-30T11:00:01.699Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-21 23:51:29,819 INFO 30096 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app\\model\\hotels.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":26177172835069412,"size":966,"blocks":8,"atimeMs":1755791489808.5715,"mtimeMs":1755787828799.4146,"ctimeMs":1755787828799.4146,"birthtimeMs":1755787828799.4146,"atime":"2025-08-21T15:51:29.809Z","mtime":"2025-08-21T14:50:28.799Z","ctime":"2025-08-21T14:50:28.799Z","birthtime":"2025-08-21T14:50:28.799Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-21 23:51:29,819 INFO 30096 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app\\model\\upload.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":12947848929670956,"size":2454,"blocks":8,"atimeMs":1755791489808.5715,"mtimeMs":1755787828799.4146,"ctimeMs":1755787828799.4146,"birthtimeMs":1755787828799.4146,"atime":"2025-08-21T15:51:29.809Z","mtime":"2025-08-21T14:50:28.799Z","ctime":"2025-08-21T14:50:28.799Z","birthtime":"2025-08-21T14:50:28.799Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-21 23:51:29,820 INFO 30096 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\config\\plugin.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":22236523160943676,"size":484,"blocks":0,"atimeMs":1755791489809.5732,"mtimeMs":1755787828801.9265,"ctimeMs":1755787828801.9265,"birthtimeMs":1753873201699.531,"atime":"2025-08-21T15:51:29.810Z","mtime":"2025-08-21T14:50:28.802Z","ctime":"2025-08-21T14:50:28.802Z","birthtime":"2025-07-30T11:00:01.700Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-21 23:51:29,820 INFO 30096 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app\\public","stat":{"dev":4026856255,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":89509042594112100,"size":0,"blocks":0,"atimeMs":1755791489819.084,"mtimeMs":1753873409640.131,"ctimeMs":1753873409640.131,"birthtimeMs":1753873409640.131,"atime":"2025-08-21T15:51:29.819Z","mtime":"2025-07-30T11:03:29.640Z","ctime":"2025-07-30T11:03:29.640Z","birthtime":"2025-07-30T11:03:29.640Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-21 23:51:31,166 WARN 30096 [agent:development] reload worker because C:\Users\<USER>\Desktop\H5地图规划\后端\app\controller\user.js change
2025-08-21 23:51:30,689 INFO 30096 [egg-watcher] Received a change event from eventSource: {"path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app.js","event":"change","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":35465847066342384,"size":617,"blocks":1,"atimeMs":1755791490688.668,"mtimeMs":1753873201692.5247,"ctimeMs":1753873201692.5247,"birthtimeMs":1753873201692.5247,"atime":"2025-08-21T15:51:30.689Z","mtime":"2025-07-30T11:00:01.693Z","ctime":"2025-07-30T11:00:01.693Z","birthtime":"2025-07-30T11:00:01.693Z"}}
2025-08-21 23:51:30,713 INFO 30096 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app\\schedule\\sendMessage.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":41095346600556400,"size":361,"blocks":0,"atimeMs":1755791490712.7026,"mtimeMs":1753873201697.529,"ctimeMs":1753873201697.529,"birthtimeMs":1753873201696.5283,"atime":"2025-08-21T15:51:30.713Z","mtime":"2025-07-30T11:00:01.698Z","ctime":"2025-07-30T11:00:01.698Z","birthtime":"2025-07-30T11:00:01.697Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-21 23:51:30,900 INFO 30096 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app\\model\\citys.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":29554872555418908,"size":1046,"blocks":8,"atimeMs":1755791490898.506,"mtimeMs":1753873201695.5273,"ctimeMs":1753873201695.5273,"birthtimeMs":1753873201695.5273,"atime":"2025-08-21T15:51:30.899Z","mtime":"2025-07-30T11:00:01.696Z","ctime":"2025-07-30T11:00:01.696Z","birthtime":"2025-07-30T11:00:01.696Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-21 23:51:30,906 INFO 30096 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app\\model\\location.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":56294995342931336,"size":1035,"blocks":8,"atimeMs":1755791490905.513,"mtimeMs":1753873201695.5273,"ctimeMs":1753873201695.5273,"birthtimeMs":1753873201695.5273,"atime":"2025-08-21T15:51:30.906Z","mtime":"2025-07-30T11:00:01.696Z","ctime":"2025-07-30T11:00:01.696Z","birthtime":"2025-07-30T11:00:01.696Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-21 23:51:30,906 INFO 30096 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app\\model\\paths.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":19421773394108896,"size":959,"blocks":8,"atimeMs":1755791490905.513,"mtimeMs":1754054505365.939,"ctimeMs":1754054505365.939,"birthtimeMs":1754054505365.939,"atime":"2025-08-21T15:51:30.906Z","mtime":"2025-08-01T13:21:45.366Z","ctime":"2025-08-01T13:21:45.366Z","birthtime":"2025-08-01T13:21:45.366Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-21 23:51:30,909 INFO 30096 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app\\model\\user.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":27303072741733956,"size":910,"blocks":8,"atimeMs":1755791490908.0186,"mtimeMs":1753873201696.5283,"ctimeMs":1753873201696.5283,"birthtimeMs":1753873201695.5273,"atime":"2025-08-21T15:51:30.908Z","mtime":"2025-07-30T11:00:01.697Z","ctime":"2025-07-30T11:00:01.697Z","birthtime":"2025-07-30T11:00:01.696Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-21 23:51:30,961 INFO 30096 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app\\controller\\citys.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":44191571344372824,"size":3769,"blocks":8,"atimeMs":1755791490960.1055,"mtimeMs":1753966098295.9656,"ctimeMs":1753966098295.9656,"birthtimeMs":1753873201693.5256,"atime":"2025-08-21T15:51:30.960Z","mtime":"2025-07-31T12:48:18.296Z","ctime":"2025-07-31T12:48:18.296Z","birthtime":"2025-07-30T11:00:01.694Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-21 23:51:30,963 INFO 30096 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app\\controller\\location.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":26458647811601544,"size":2919,"blocks":8,"atimeMs":1755791490962.1074,"mtimeMs":1753966098295.9656,"ctimeMs":1753966098295.9656,"birthtimeMs":1753873201694.526,"atime":"2025-08-21T15:51:30.962Z","mtime":"2025-07-31T12:48:18.296Z","ctime":"2025-07-31T12:48:18.296Z","birthtime":"2025-07-30T11:00:01.695Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-21 23:51:30,963 INFO 30096 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app\\controller\\paths.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":6755399442129368,"size":3793,"blocks":8,"atimeMs":1755791490962.1074,"mtimeMs":1754061019445.608,"ctimeMs":1754061019445.608,"birthtimeMs":1754054505364.9377,"atime":"2025-08-21T15:51:30.962Z","mtime":"2025-08-01T15:10:19.446Z","ctime":"2025-08-01T15:10:19.446Z","birthtime":"2025-08-01T13:21:45.365Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-21 23:51:30,964 INFO 30096 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app\\controller\\user.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":27866022695154830,"size":4754,"blocks":16,"atimeMs":1755791490963.1084,"mtimeMs":1754054505365.939,"ctimeMs":1754054505365.939,"birthtimeMs":1753873201694.526,"atime":"2025-08-21T15:51:30.963Z","mtime":"2025-08-01T13:21:45.366Z","ctime":"2025-08-01T13:21:45.366Z","birthtime":"2025-07-30T11:00:01.695Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-21 23:51:31,003 WARN 30096 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-21 23:51:32,115 WARN 30096 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-21 23:51:36,598 WARN 30096 [agent:development] reload worker because C:\Users\<USER>\Desktop\H5地图规划\后端\app\utils\tool.js change
2025-08-21 23:51:36,389 INFO 30096 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app\\utils\\tool.js","stat":{"dev":4026856255,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":73183493945572340,"size":2847,"blocks":8,"atimeMs":1755791496388.2512,"mtimeMs":1753873201698.53,"ctimeMs":1753873201698.53,"birthtimeMs":1753873201698.53,"atime":"2025-08-21T15:51:36.388Z","mtime":"2025-07-30T11:00:01.699Z","ctime":"2025-07-30T11:00:01.699Z","birthtime":"2025-07-30T11:00:01.699Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-21 23:51:37,668 WARN 30096 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-21 23:52:06,597 INFO 30144 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-21 23:52:06,339 INFO 30144 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\logs\\plan-map-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"plan-map-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-08-21 23:52:06,547 INFO 30144 [egg:core] dump config after load, 3ms
2025-08-21 23:52:06,575 INFO 30144 [egg-watcher] Start watching: ["C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app","C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\config","C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\mocks","C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\mocks_proxy","C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app.js"]
2025-08-21 23:52:06,575 INFO 30144 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app"
2025-08-21 23:52:06,575 INFO 30144 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\config"
2025-08-21 23:52:06,575 INFO 30144 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\mocks"
2025-08-21 23:52:06,575 INFO 30144 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\mocks_proxy"
2025-08-21 23:52:06,575 INFO 30144 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\app.js"
2025-08-21 23:52:06,576 INFO 30144 [egg-watcher:agent] watcher start success
2025-08-21 23:52:06,603 INFO 30144 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [386ms] - #0 Process Start
                         ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [358ms] - #1 Application Start
                          ▇ [10ms] - #2 Load Plugin
                           ▇ [16ms] - #3 Load Config
                           ▇ [0ms] - #4 Require(0) config/config.default.js
                           ▇ [0ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                           ▇ [1ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                           ▇ [0ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                           ▇ [0ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                           ▇ [1ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                           ▇ [0ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                           ▇ [0ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                           ▇ [1ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                           ▇ [0ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                           ▇ [1ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                           ▇ [0ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                           ▇ [0ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                           ▇ [0ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                           ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                           ▇ [0ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                           ▇ [0ms] - #20 Require(16) node_modules/egg/config/config.default.js
                           ▇ [0ms] - #21 Require(17) config/config.default.js
                           ▇ [0ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                           ▇ [1ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                            ▇ [1ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                            ▇ [0ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                            ▇ [0ms] - #26 Require(22) node_modules/egg/config/config.local.js
                            ▇▇ [30ms] - #27 Load extend/agent.js
                            ▇ [1ms] - #28 Require(23) node_modules/egg-security/app/extend/agent.js
                            ▇ [26ms] - #29 Require(24) node_modules/egg-schedule/app/extend/agent.js
                              ▇ [0ms] - #30 Require(25) node_modules/egg-logrotator/app/extend/agent.js
                              ▇ [19ms] - #31 Load extend/context.js
                              ▇ [5ms] - #32 Require(26) node_modules/egg-security/app/extend/context.js
                              ▇ [1ms] - #33 Require(27) node_modules/egg-jsonp/app/extend/context.js
                              ▇ [0ms] - #34 Require(28) node_modules/egg-i18n/app/extend/context.js
                              ▇ [9ms] - #35 Require(29) node_modules/egg-multipart/app/extend/context.js
                               ▇ [1ms] - #36 Require(30) node_modules/egg-view/app/extend/context.js
                               ▇ [1ms] - #37 Require(31) node_modules/egg/app/extend/context.js
                               ▇ [11ms] - #38 Load agent.js
                               ▇ [0ms] - #39 Require(32) node_modules/egg-security/agent.js
                               ▇ [0ms] - #40 Require(33) node_modules/egg-onerror/agent.js
                               ▇ [3ms] - #41 Require(34) node_modules/egg-watcher/agent.js
                               ▇ [0ms] - #42 Require(35) node_modules/egg-schedule/agent.js
                               ▇ [4ms] - #43 Require(36) node_modules/egg-development/agent.js
                                ▇ [0ms] - #44 Require(37) node_modules/egg-logrotator/agent.js
                                ▇ [0ms] - #45 Require(38) node_modules/egg-sequelize/agent.js
                                ▇ [0ms] - #46 Require(39) node_modules/egg/agent.js
                                ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [235ms] - #47 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                ▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [220ms] - #48 Before Start in node_modules/egg-schedule/agent.js:12:9
                                ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [258ms] - #49 Before Start in node_modules/egg-development/agent.js:9:9
                                            ▇ [15ms] - #50 Load "Symbol(model)" to Application
                                              ▇▇▇ [54ms] - #51 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
2025-08-21 23:52:06,603 INFO 30144 [egg:core] dump config after ready, 3ms

/*
 Navicat Premium Data Transfer

 Source Server         : 本地
 Source Server Type    : MySQL
 Source Server Version : 50740
 Source Host           : localhost:3306
 Source Schema         : plan-map

 Target Server Type    : MySQL
 Target Server Version : 50740
 File Encoding         : 65001

 Date: 21/08/2025 17:37:17
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for citys
-- ----------------------------
DROP TABLE IF EXISTS `citys`;
CREATE TABLE `citys`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `province` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '省份',
  `city` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '城市',
  `district` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '行政区',
  `lng` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '经度',
  `lat` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '纬度',
  `active` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否默认',
  `time` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '时间',
  `uid` int(11) NULL DEFAULT NULL COMMENT '所属用户 关联user',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '中心城市表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of citys
-- ----------------------------
INSERT INTO `citys` VALUES (1, '广西壮族自治区', '南宁市', '青秀区', '108.40482313735464', '22.818248982276994', '0', '2025-07-31 09:58:05', 1);
INSERT INTO `citys` VALUES (2, '贵州省', '贵阳市', '云岩区', '106.71447251732481', '26.604028737423626', '1', '2025-07-31 15:48:53', 1);

-- ----------------------------
-- Table structure for foods
-- ----------------------------
DROP TABLE IF EXISTS `foods`;
CREATE TABLE `foods`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '标题',
  `lid` int(11) NULL DEFAULT NULL COMMENT '地点信息 关联location',
  `pid` int(11) NULL DEFAULT NULL COMMENT '关联路径 关联paths',
  `img` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '图片',
  `time` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '美食表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of foods
-- ----------------------------
INSERT INTO `foods` VALUES (2, '乔治队长(咖啡店,贵 可以装逼)', 13, 1, '/uploads/1755768962576_agv563ftf.jpg', '2025-08-21 17:34:17');

-- ----------------------------
-- Table structure for hotels
-- ----------------------------
DROP TABLE IF EXISTS `hotels`;
CREATE TABLE `hotels`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '标题',
  `lid` int(11) NULL DEFAULT NULL COMMENT '地点信息 关联location',
  `pid` int(11) NULL DEFAULT NULL COMMENT '关联路径 关联paths',
  `img` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '图片',
  `time` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '住宿表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of hotels
-- ----------------------------
INSERT INTO `hotels` VALUES (13, '9.5 9.6(待定) 住宿安顺 王庄安置房', 10, 1, '/uploads/1755764103888_cgo6jfg2a.png', '2025-08-21 16:15:05');
INSERT INTO `hotels` VALUES (14, '9.4 住宿贵阳 中科怡璟', 9, 1, '/uploads/1755764213632_280gl04ix.png', '2025-08-21 16:16:54');

-- ----------------------------
-- Table structure for location
-- ----------------------------
DROP TABLE IF EXISTS `location`;
CREATE TABLE `location`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名称',
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '简介',
  `address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '地址',
  `lng` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '经度',
  `lat` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '纬度',
  `time` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '时间',
  `uid` int(11) NULL DEFAULT NULL COMMENT '所属用户 关联user',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '标点表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of location
-- ----------------------------
INSERT INTO `location` VALUES (1, '罗秧河营地（溯溪）', '', '贵州省安顺市关岭布依族苗族自治县', '105.37664408786677', '25.908575249188818', '2025-07-31 15:53:47', 1);
INSERT INTO `location` VALUES (2, '织金奢香洞（洞穴奇观，需徒步）', '', '贵州省毕节市织金县织金桂果旅游区', '105.90110898002804', '26.591265807253077', '2025-07-31 15:55:46', 1);
INSERT INTO `location` VALUES (3, '荔波小七孔（桥梁著名景点）', '', '贵州省黔南布依族苗族自治州荔波县206省道', '107.71430586316846', '25.260091257974064', '2025-07-31 15:56:17', 1);
INSERT INTO `location` VALUES (4, '梵净山景区（氧气山林）', '', '贵州省铜仁市江口县梵净山游客中心旁', '108.78291285001946', '27.847484143899955', '2025-07-31 15:56:50', 1);
INSERT INTO `location` VALUES (5, '关岭冰臼（石头河床景观）', '', '安顺市关岭布依族苗族自治县断桥镇黄果树瀑布下游295公里处', '105.73367004319542', '25.787454997471226', '2025-07-31 15:59:19', 1);
INSERT INTO `location` VALUES (6, '贵阳站', '', '贵州省贵阳市南明区四通街1号', '106.7088306197407', '26.56272241247931', '2025-07-31 16:18:53', 1);
INSERT INTO `location` VALUES (7, '高过河漂流（漂流）', '', '黔东南苗族侗族自治州镇远县羊场镇(高过河漂流景区)高过河风景名胜区东边4公里', '108.31828615396073', '27.260187575961382', '2025-07-31 16:24:33', 1);
INSERT INTO `location` VALUES (8, '洛北河漂流（漂流）', '', '贵州省黔南布依族苗族自治州贵定县G321(沪瑞线)', '107.17353097685123', '26.503635962026426', '2025-07-31 16:24:57', 1);
INSERT INTO `location` VALUES (9, '中科怡璟(9.4住宿)', '', '贵阳市观山湖区龙海路与龙滩坝路交汇处', '106.62110156468194', '26.654496817816575', '2025-08-21 16:11:16', 1);
INSERT INTO `location` VALUES (10, '王庄安置房(9.5住宿 9.6待定)', '', '安顺市西秀区天晟国际灯饰城东北侧约230米', '105.9150935307111', '26.239901227816222', '2025-08-21 16:13:03', 1);
INSERT INTO `location` VALUES (13, 'CaptainGeorge乔治队长(文昌店)', '', '阁街道文昌北路柳园一宏泰文景阁1层1-4号', '106.72637075346164', '26.58460118528469', '2025-08-21 17:33:16', 1);

-- ----------------------------
-- Table structure for paths
-- ----------------------------
DROP TABLE IF EXISTS `paths`;
CREATE TABLE `paths`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '路径编号',
  `title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '路径名称',
  `uid` int(11) NULL DEFAULT NULL COMMENT '所属用户id 关联user',
  `jsons` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '路径详情JSON',
  `time` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '路径规划表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of paths
-- ----------------------------
INSERT INTO `paths` VALUES (1, 'PATH_MDSM0MKR_0PEGO', '测试', 1, '[{\"id\":14,\"name\":\"贵阳站\",\"description\":\"\",\"address\":\"贵州省贵阳市南明区四通街1号\",\"lng\":\"106.7088306197407\",\"lat\":\"26.56272241247931\",\"time\":\"2025-07-31 16:18:53\",\"uid\":1},{\"id\":16,\"name\":\"洛北河漂流（漂流）\",\"description\":\"\",\"address\":\"贵州省黔南布依族苗族自治州贵定县G321(沪瑞线)\",\"lng\":\"107.17353097685123\",\"lat\":\"26.503635962026426\",\"time\":\"2025-07-31 16:24:57\",\"uid\":1},{\"id\":13,\"name\":\"关岭冰臼（石头河床景观）\",\"description\":\"\",\"address\":\"安顺市关岭布依族苗族自治县断桥镇黄果树瀑布下游295公里处\",\"lng\":\"105.73367004319542\",\"lat\":\"25.787454997471226\",\"time\":\"2025-07-31 15:59:19\",\"uid\":1},{\"id\":12,\"name\":\"梵净山景区（氧气山林）\",\"description\":\"\",\"address\":\"贵州省铜仁市江口县梵净山游客中心旁\",\"lng\":\"108.78291285001946\",\"lat\":\"27.847484143899955\",\"time\":\"2025-07-31 15:56:50\",\"uid\":1},{\"id\":11,\"name\":\"荔波小七孔（桥梁著名景点）\",\"description\":\"\",\"address\":\"贵州省黔南布依族苗族自治州荔波县206省道\",\"lng\":\"107.71430586316846\",\"lat\":\"25.260091257974064\",\"time\":\"2025-07-31 15:56:17\",\"uid\":1},{\"id\":15,\"name\":\"高过河漂流（漂流）\",\"description\":\"\",\"address\":\"黔东南苗族侗族自治州镇远县羊场镇(高过河漂流景区)高过河风景名胜区东边4公里\",\"lng\":\"108.31828615396073\",\"lat\":\"27.260187575961382\",\"time\":\"2025-07-31 16:24:33\",\"uid\":1},{\"id\":10,\"name\":\"织金奢香洞（洞穴奇观，需徒步）\",\"description\":\"\",\"address\":\"贵州省毕节市织金县织金桂果旅游区\",\"lng\":\"105.90110898002804\",\"lat\":\"26.591265807253077\",\"time\":\"2025-07-31 15:55:46\",\"uid\":1},{\"id\":9,\"name\":\"罗秧河营地（溯溪）\",\"description\":\"\",\"address\":\"贵州省安顺市关岭布依族苗族自治县\",\"lng\":\"105.37664408786677\",\"lat\":\"25.908575249188818\",\"time\":\"2025-07-31 15:53:47\",\"uid\":1}]', '2025-08-01 17:16:54');

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名称',
  `user` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '账号',
  `pwd` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '密码',
  `time` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user
-- ----------------------------
INSERT INTO `user` VALUES (1, 'yam', 'yam', 'yam', '2025-07-30 11:30:00');

SET FOREIGN_KEY_CHECKS = 1;

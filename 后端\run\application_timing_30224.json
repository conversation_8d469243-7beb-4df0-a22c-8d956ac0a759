[{"name": "Process Start", "start": 1755791526788, "end": 1755791527186, "duration": 398, "pid": 30224, "index": 0}, {"name": "Application Start", "start": 1755791527186, "end": 1755791527601, "duration": 415, "pid": 30224, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1755791527194, "end": 1755791527204, "duration": 10, "pid": 30224, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1755791527204, "end": 1755791527215, "duration": 11, "pid": 30224, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1755791527204, "end": 1755791527204, "duration": 0, "pid": 30224, "index": 4}, {"name": "Require(1) node_modules/egg-session/config/config.default.js", "start": 1755791527206, "end": 1755791527206, "duration": 0, "pid": 30224, "index": 5}, {"name": "Require(2) node_modules/egg-security/config/config.default.js", "start": 1755791527206, "end": 1755791527206, "duration": 0, "pid": 30224, "index": 6}, {"name": "Require(3) node_modules/egg-jsonp/config/config.default.js", "start": 1755791527207, "end": 1755791527207, "duration": 0, "pid": 30224, "index": 7}, {"name": "Require(4) node_modules/egg-onerror/config/config.default.js", "start": 1755791527207, "end": 1755791527207, "duration": 0, "pid": 30224, "index": 8}, {"name": "Require(5) node_modules/egg-i18n/config/config.default.js", "start": 1755791527208, "end": 1755791527208, "duration": 0, "pid": 30224, "index": 9}, {"name": "Require(6) node_modules/egg-watcher/config/config.default.js", "start": 1755791527208, "end": 1755791527208, "duration": 0, "pid": 30224, "index": 10}, {"name": "Require(7) node_modules/egg-schedule/config/config.default.js", "start": 1755791527209, "end": 1755791527209, "duration": 0, "pid": 30224, "index": 11}, {"name": "Require(8) node_modules/egg-multipart/config/config.default.js", "start": 1755791527209, "end": 1755791527209, "duration": 0, "pid": 30224, "index": 12}, {"name": "Require(9) node_modules/egg-development/config/config.default.js", "start": 1755791527209, "end": 1755791527210, "duration": 1, "pid": 30224, "index": 13}, {"name": "Require(10) node_modules/egg-logrotator/config/config.default.js", "start": 1755791527210, "end": 1755791527210, "duration": 0, "pid": 30224, "index": 14}, {"name": "Require(11) node_modules/egg-static/config/config.default.js", "start": 1755791527210, "end": 1755791527210, "duration": 0, "pid": 30224, "index": 15}, {"name": "Require(12) node_modules/egg-view/config/config.default.js", "start": 1755791527211, "end": 1755791527211, "duration": 0, "pid": 30224, "index": 16}, {"name": "Require(13) node_modules/egg-sequelize/config/config.default.js", "start": 1755791527211, "end": 1755791527211, "duration": 0, "pid": 30224, "index": 17}, {"name": "Require(14) node_modules/egg-cors/config/config.default.js", "start": 1755791527211, "end": 1755791527211, "duration": 0, "pid": 30224, "index": 18}, {"name": "Require(15) node_modules/egg-view-nunjucks/config/config.default.js", "start": 1755791527212, "end": 1755791527212, "duration": 0, "pid": 30224, "index": 19}, {"name": "Require(16) node_modules/egg/config/config.default.js", "start": 1755791527212, "end": 1755791527212, "duration": 0, "pid": 30224, "index": 20}, {"name": "Require(17) config/config.default.js", "start": 1755791527213, "end": 1755791527213, "duration": 0, "pid": 30224, "index": 21}, {"name": "Require(18) node_modules/egg-security/config/config.local.js", "start": 1755791527213, "end": 1755791527213, "duration": 0, "pid": 30224, "index": 22}, {"name": "Require(19) node_modules/egg-watcher/config/config.local.js", "start": 1755791527214, "end": 1755791527214, "duration": 0, "pid": 30224, "index": 23}, {"name": "Require(20) node_modules/egg-view/config/config.local.js", "start": 1755791527214, "end": 1755791527214, "duration": 0, "pid": 30224, "index": 24}, {"name": "Require(21) node_modules/egg-view-nunjucks/config/config.local.js", "start": 1755791527215, "end": 1755791527215, "duration": 0, "pid": 30224, "index": 25}, {"name": "Require(22) node_modules/egg/config/config.local.js", "start": 1755791527215, "end": 1755791527215, "duration": 0, "pid": 30224, "index": 26}, {"name": "Load extend/application.js", "start": 1755791527216, "end": 1755791527238, "duration": 22, "pid": 30224, "index": 27}, {"name": "Require(23) node_modules/egg-session/app/extend/application.js", "start": 1755791527216, "end": 1755791527216, "duration": 0, "pid": 30224, "index": 28}, {"name": "Require(24) node_modules/egg-security/app/extend/application.js", "start": 1755791527217, "end": 1755791527217, "duration": 0, "pid": 30224, "index": 29}, {"name": "Require(25) node_modules/egg-jsonp/app/extend/application.js", "start": 1755791527217, "end": 1755791527219, "duration": 2, "pid": 30224, "index": 30}, {"name": "Require(26) node_modules/egg-schedule/app/extend/application.js", "start": 1755791527220, "end": 1755791527221, "duration": 1, "pid": 30224, "index": 31}, {"name": "Require(27) node_modules/egg-logrotator/app/extend/application.js", "start": 1755791527222, "end": 1755791527222, "duration": 0, "pid": 30224, "index": 32}, {"name": "Require(28) node_modules/egg-view/app/extend/application.js", "start": 1755791527223, "end": 1755791527223, "duration": 0, "pid": 30224, "index": 33}, {"name": "Require(29) node_modules/egg-view-nunjucks/app/extend/application.js", "start": 1755791527224, "end": 1755791527237, "duration": 13, "pid": 30224, "index": 34}, {"name": "Load extend/request.js", "start": 1755791527238, "end": 1755791527241, "duration": 3, "pid": 30224, "index": 35}, {"name": "Require(30) node_modules/egg/app/extend/request.js", "start": 1755791527239, "end": 1755791527240, "duration": 1, "pid": 30224, "index": 36}, {"name": "Load extend/response.js", "start": 1755791527241, "end": 1755791527244, "duration": 3, "pid": 30224, "index": 37}, {"name": "Require(31) node_modules/egg/app/extend/response.js", "start": 1755791527242, "end": 1755791527243, "duration": 1, "pid": 30224, "index": 38}, {"name": "Load extend/context.js", "start": 1755791527244, "end": 1755791527261, "duration": 17, "pid": 30224, "index": 39}, {"name": "Require(32) node_modules/egg-security/app/extend/context.js", "start": 1755791527244, "end": 1755791527249, "duration": 5, "pid": 30224, "index": 40}, {"name": "Require(33) node_modules/egg-jsonp/app/extend/context.js", "start": 1755791527249, "end": 1755791527249, "duration": 0, "pid": 30224, "index": 41}, {"name": "Require(34) node_modules/egg-i18n/app/extend/context.js", "start": 1755791527250, "end": 1755791527250, "duration": 0, "pid": 30224, "index": 42}, {"name": "Require(35) node_modules/egg-multipart/app/extend/context.js", "start": 1755791527250, "end": 1755791527258, "duration": 8, "pid": 30224, "index": 43}, {"name": "Require(36) node_modules/egg-view/app/extend/context.js", "start": 1755791527259, "end": 1755791527259, "duration": 0, "pid": 30224, "index": 44}, {"name": "Require(37) node_modules/egg/app/extend/context.js", "start": 1755791527259, "end": 1755791527260, "duration": 1, "pid": 30224, "index": 45}, {"name": "Load extend/helper.js", "start": 1755791527261, "end": 1755791527272, "duration": 11, "pid": 30224, "index": 46}, {"name": "Require(38) node_modules/egg-security/app/extend/helper.js", "start": 1755791527262, "end": 1755791527269, "duration": 7, "pid": 30224, "index": 47}, {"name": "Require(39) node_modules/egg/app/extend/helper.js", "start": 1755791527270, "end": 1755791527270, "duration": 0, "pid": 30224, "index": 48}, {"name": "Load app.js", "start": 1755791527272, "end": 1755791527289, "duration": 17, "pid": 30224, "index": 49}, {"name": "Require(40) node_modules/egg-session/app.js", "start": 1755791527272, "end": 1755791527272, "duration": 0, "pid": 30224, "index": 50}, {"name": "Require(41) node_modules/egg-security/app.js", "start": 1755791527272, "end": 1755791527273, "duration": 1, "pid": 30224, "index": 51}, {"name": "Require(42) node_modules/egg-onerror/app.js", "start": 1755791527273, "end": 1755791527277, "duration": 4, "pid": 30224, "index": 52}, {"name": "Require(43) node_modules/egg-i18n/app.js", "start": 1755791527277, "end": 1755791527282, "duration": 5, "pid": 30224, "index": 53}, {"name": "Require(44) node_modules/egg-watcher/app.js", "start": 1755791527282, "end": 1755791527285, "duration": 3, "pid": 30224, "index": 54}, {"name": "Require(45) node_modules/egg-schedule/app.js", "start": 1755791527285, "end": 1755791527286, "duration": 1, "pid": 30224, "index": 55}, {"name": "Require(46) node_modules/egg-multipart/app.js", "start": 1755791527286, "end": 1755791527286, "duration": 0, "pid": 30224, "index": 56}, {"name": "Require(47) node_modules/egg-development/app.js", "start": 1755791527287, "end": 1755791527287, "duration": 0, "pid": 30224, "index": 57}, {"name": "Require(48) node_modules/egg-logrotator/app.js", "start": 1755791527287, "end": 1755791527287, "duration": 0, "pid": 30224, "index": 58}, {"name": "Require(49) node_modules/egg-static/app.js", "start": 1755791527287, "end": 1755791527287, "duration": 0, "pid": 30224, "index": 59}, {"name": "Require(50) node_modules/egg-sequelize/app.js", "start": 1755791527287, "end": 1755791527288, "duration": 1, "pid": 30224, "index": 60}, {"name": "Require(51) node_modules/egg-cors/app.js", "start": 1755791527288, "end": 1755791527288, "duration": 0, "pid": 30224, "index": 61}, {"name": "Require(52) node_modules/egg-view-nunjucks/app.js", "start": 1755791527288, "end": 1755791527288, "duration": 0, "pid": 30224, "index": 62}, {"name": "Require(53) app.js", "start": 1755791527288, "end": 1755791527289, "duration": 1, "pid": 30224, "index": 63}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1755791527294, "end": 1755791527594, "duration": 300, "pid": 30224, "index": 64}, {"name": "Load \"Symbol(model)\" to Application", "start": 1755791527503, "end": 1755791527513, "duration": 10, "pid": 30224, "index": 65}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1755791527514, "end": 1755791527601, "duration": 87, "pid": 30224, "index": 66}, {"name": "Did Load in app.js:didLoad", "start": 1755791527515, "end": 1755791527582, "duration": 67, "pid": 30224, "index": 67}, {"name": "Load Service", "start": 1755791527515, "end": 1755791527519, "duration": 4, "pid": 30224, "index": 68}, {"name": "Load \"service\" to Context", "start": 1755791527515, "end": 1755791527519, "duration": 4, "pid": 30224, "index": 69}, {"name": "Load Middleware", "start": 1755791527519, "end": 1755791527565, "duration": 46, "pid": 30224, "index": 70}, {"name": "Load \"middlewares\" to Application", "start": 1755791527519, "end": 1755791527560, "duration": 41, "pid": 30224, "index": 71}, {"name": "Load Controller", "start": 1755791527565, "end": 1755791527569, "duration": 4, "pid": 30224, "index": 72}, {"name": "Load \"controller\" to Application", "start": 1755791527565, "end": 1755791527569, "duration": 4, "pid": 30224, "index": 73}, {"name": "Load Router", "start": 1755791527569, "end": 1755791527571, "duration": 2, "pid": 30224, "index": 74}, {"name": "Require(54) app/router.js", "start": 1755791527569, "end": 1755791527569, "duration": 0, "pid": 30224, "index": 75}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1755791527569, "end": 1755791527582, "duration": 13, "pid": 30224, "index": 76}, {"name": "Will Ready in app.js:will<PERSON><PERSON>y", "start": 1755791527601, "end": 1755791527601, "duration": 0, "pid": 30224, "index": 77}]
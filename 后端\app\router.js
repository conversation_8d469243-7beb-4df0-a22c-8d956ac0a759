/**
 * @param {Egg.Application} app - egg application
 */
module.exports = (app) => {
  const { router, controller } = app;

  // 城市管理路由
  router.post("/citys/list", controller.citys.list);
  router.post("/citys/save", controller.citys.save);
  router.post("/citys/delete", controller.citys.delete);
  router.post("/citys/setdefault", controller.citys.setDefault);

  // 位置管理路由;
  router.post("/location/list", controller.location.list);
  router.post("/location/save", controller.location.save);
  router.post("/location/delete", controller.location.delete);

  // 用户管理路由;
  router.post("/user/list", controller.user.list);
  router.post("/user/save", controller.user.save);
  router.post("/user/delete", controller.user.delete);
  router.post("/user/login", controller.user.login);
  router.post("/user/register", controller.user.register);

  // 路径规划路由
  router.post("/paths/list", controller.paths.list);
  router.post("/paths/save", controller.paths.save);
  router.post("/paths/delete", controller.paths.delete);

  //美食表
  router.post("/foods/list", controller.foods.list);
  router.post("/foods/save", controller.foods.save);
  router.post("/foods/delete", controller.foods.delete);

  //住宿表
  router.post("/hotels/list", controller.hotels.list);
  router.post("/hotels/save", controller.hotels.save);
  router.post("/hotels/delete", controller.hotels.delete);

  // 文件上传路由
  router.post("/upload/file", controller.upload.upload);
  router.post("/upload/list", controller.upload.list);
  router.post("/upload/delete", controller.upload.delete);
  router.get("/upload/download/:fileName", controller.upload.download);
  router.get("/upload/stats", controller.upload.getStats);

  // 配置其他前端页面路由
  router.get("/", async (ctx) => {
    // 访问目录下的view文件夹中的index.html
    await ctx.render("upload.html");
  });
};

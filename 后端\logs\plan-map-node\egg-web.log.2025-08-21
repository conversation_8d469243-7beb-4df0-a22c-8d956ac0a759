2025-08-04 19:59:13,752 INFO 20076 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\logs\\plan-map-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"plan-map-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"application","localStorage":{"enabled":false}}
2025-08-04 19:59:13,752 INFO 20076 [egg-multipart] stream mode enable
2025-08-04 19:59:14,032 INFO 20076 [egg-static] starting static serve / -> C:\Users\<USER>\Desktop\H5地图规划\后端\view
2025-08-04 19:59:14,034 INFO 20076 [egg-security] use methodnoallow middleware
2025-08-04 19:59:14,034 INFO 20076 [egg-security] use noopen middleware
2025-08-04 19:59:14,035 INFO 20076 [egg-security] use nosniff middleware
2025-08-04 19:59:14,036 INFO 20076 [egg-security] use xssProtection middleware
2025-08-04 19:59:14,036 INFO 20076 [egg-security] use xframe middleware
2025-08-04 19:59:14,037 INFO 20076 [egg-security] use dta middleware
2025-08-04 19:59:14,037 INFO 20076 [egg-security] compose 6 middlewares into one security middleware
2025-08-04 19:59:14,045 INFO 20076 [egg:core] dump config after load, 4ms
2025-08-04 19:59:14,065 INFO 20076 [egg-watcher:application] watcher start success
2025-08-04 19:59:14,077 INFO 20076 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [381ms] - #0 Process Start
                      ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [453ms] - #1 Application Start
                       ▇ [9ms] - #2 Load Plugin
                       ▇ [11ms] - #3 Load Config
                       ▇ [0ms] - #4 Require(0) config/config.default.js
                       ▇ [0ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                       ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                       ▇ [0ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                       ▇ [0ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                       ▇ [0ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                        ▇ [0ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                        ▇ [0ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                        ▇ [1ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                        ▇ [0ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                        ▇ [0ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                        ▇ [0ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                        ▇ [0ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                        ▇ [1ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                        ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                        ▇ [0ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                        ▇ [1ms] - #20 Require(16) node_modules/egg/config/config.default.js
                        ▇ [0ms] - #21 Require(17) config/config.default.js
                        ▇ [0ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                        ▇ [0ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                        ▇ [0ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                        ▇ [0ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                        ▇ [1ms] - #26 Require(22) node_modules/egg/config/config.local.js
                        ▇ [33ms] - #27 Load extend/application.js
                        ▇ [0ms] - #28 Require(23) node_modules/egg-session/app/extend/application.js
                        ▇ [0ms] - #29 Require(24) node_modules/egg-security/app/extend/application.js
                        ▇ [2ms] - #30 Require(25) node_modules/egg-jsonp/app/extend/application.js
                        ▇ [2ms] - #31 Require(26) node_modules/egg-schedule/app/extend/application.js
                        ▇ [1ms] - #32 Require(27) node_modules/egg-logrotator/app/extend/application.js
                        ▇ [1ms] - #33 Require(28) node_modules/egg-view/app/extend/application.js
                         ▇ [22ms] - #34 Require(29) node_modules/egg-view-nunjucks/app/extend/application.js
                          ▇ [4ms] - #35 Load extend/request.js
                          ▇ [0ms] - #36 Require(30) node_modules/egg/app/extend/request.js
                          ▇ [3ms] - #37 Load extend/response.js
                          ▇ [1ms] - #38 Require(31) node_modules/egg/app/extend/response.js
                          ▇ [18ms] - #39 Load extend/context.js
                          ▇ [5ms] - #40 Require(32) node_modules/egg-security/app/extend/context.js
                           ▇ [1ms] - #41 Require(33) node_modules/egg-jsonp/app/extend/context.js
                           ▇ [0ms] - #42 Require(34) node_modules/egg-i18n/app/extend/context.js
                           ▇ [8ms] - #43 Require(35) node_modules/egg-multipart/app/extend/context.js
                           ▇ [1ms] - #44 Require(36) node_modules/egg-view/app/extend/context.js
                           ▇ [1ms] - #45 Require(37) node_modules/egg/app/extend/context.js
                           ▇ [16ms] - #46 Load extend/helper.js
                           ▇ [14ms] - #47 Require(38) node_modules/egg-security/app/extend/helper.js
                            ▇ [0ms] - #48 Require(39) node_modules/egg/app/extend/helper.js
                            ▇ [27ms] - #49 Load app.js
                            ▇ [0ms] - #50 Require(40) node_modules/egg-session/app.js
                            ▇ [1ms] - #51 Require(41) node_modules/egg-security/app.js
                             ▇ [8ms] - #52 Require(42) node_modules/egg-onerror/app.js
                             ▇ [8ms] - #53 Require(43) node_modules/egg-i18n/app.js
                             ▇ [3ms] - #54 Require(44) node_modules/egg-watcher/app.js
                              ▇ [1ms] - #55 Require(45) node_modules/egg-schedule/app.js
                              ▇ [1ms] - #56 Require(46) node_modules/egg-multipart/app.js
                              ▇ [0ms] - #57 Require(47) node_modules/egg-development/app.js
                              ▇ [0ms] - #58 Require(48) node_modules/egg-logrotator/app.js
                              ▇ [0ms] - #59 Require(49) node_modules/egg-static/app.js
                              ▇ [0ms] - #60 Require(50) node_modules/egg-sequelize/app.js
                              ▇ [0ms] - #61 Require(51) node_modules/egg-cors/app.js
                              ▇ [0ms] - #62 Require(52) node_modules/egg-view-nunjucks/app.js
                              ▇ [0ms] - #63 Require(53) app.js
                              ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [310ms] - #64 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                          ▇ [8ms] - #65 Load "Symbol(model)" to Application
                                           ▇▇▇▇▇▇ [110ms] - #66 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
                                           ▇▇▇▇▇ [88ms] - #67 Did Load in app.js:didLoad
                                           ▇ [4ms] - #68 Load Service
                                           ▇ [4ms] - #69 Load "service" to Context
                                           ▇▇▇▇ [69ms] - #70 Load Middleware
                                           ▇▇▇ [63ms] - #71 Load "middlewares" to Application
                                               ▇ [3ms] - #72 Load Controller
                                               ▇ [3ms] - #73 Load "controller" to Application
                                               ▇ [1ms] - #74 Load Router
                                               ▇ [0ms] - #75 Require(54) app/router.js
                                               ▇ [11ms] - #76 Before Start in node_modules/egg-core/lib/egg.js:328:10
                                                 ▇ [0ms] - #77 Will Ready in app.js:willReady
2025-08-04 19:59:14,077 INFO 20076 [egg:core] dump config after ready, 3ms
2025-08-04 19:59:40,902 INFO 12312 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\logs\\plan-map-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"plan-map-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"application","localStorage":{"enabled":false}}
2025-08-04 19:59:40,902 INFO 12312 [egg-multipart] stream mode enable
2025-08-04 19:59:41,159 INFO 12312 [egg-static] starting static serve / -> C:\Users\<USER>\Desktop\H5地图规划\view
2025-08-04 19:59:41,161 INFO 12312 [egg-security] use methodnoallow middleware
2025-08-04 19:59:41,161 INFO 12312 [egg-security] use noopen middleware
2025-08-04 19:59:41,162 INFO 12312 [egg-security] use nosniff middleware
2025-08-04 19:59:41,162 INFO 12312 [egg-security] use xssProtection middleware
2025-08-04 19:59:41,163 INFO 12312 [egg-security] use xframe middleware
2025-08-04 19:59:41,163 INFO 12312 [egg-security] use dta middleware
2025-08-04 19:59:41,163 INFO 12312 [egg-security] compose 6 middlewares into one security middleware
2025-08-04 19:59:41,171 INFO 12312 [egg:core] dump config after load, 4ms
2025-08-04 19:59:41,184 INFO 12312 [egg-watcher:application] watcher start success
2025-08-04 19:59:41,203 INFO 12312 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [384ms] - #0 Process Start
                        ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [399ms] - #1 Application Start
                        ▇ [8ms] - #2 Load Plugin
                         ▇ [12ms] - #3 Load Config
                         ▇ [0ms] - #4 Require(0) config/config.default.js
                         ▇ [0ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                         ▇ [1ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                         ▇ [0ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                         ▇ [1ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                         ▇ [0ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                         ▇ [1ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                         ▇ [0ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                         ▇ [0ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                         ▇ [0ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                         ▇ [0ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                         ▇ [1ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                         ▇ [0ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                         ▇ [0ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                         ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                         ▇ [0ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                         ▇ [0ms] - #20 Require(16) node_modules/egg/config/config.default.js
                         ▇ [0ms] - #21 Require(17) config/config.default.js
                         ▇ [1ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                          ▇ [0ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                          ▇ [0ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                          ▇ [0ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                          ▇ [0ms] - #26 Require(22) node_modules/egg/config/config.local.js
                          ▇ [21ms] - #27 Load extend/application.js
                          ▇ [0ms] - #28 Require(23) node_modules/egg-session/app/extend/application.js
                          ▇ [0ms] - #29 Require(24) node_modules/egg-security/app/extend/application.js
                          ▇ [1ms] - #30 Require(25) node_modules/egg-jsonp/app/extend/application.js
                          ▇ [2ms] - #31 Require(26) node_modules/egg-schedule/app/extend/application.js
                          ▇ [0ms] - #32 Require(27) node_modules/egg-logrotator/app/extend/application.js
                          ▇ [1ms] - #33 Require(28) node_modules/egg-view/app/extend/application.js
                          ▇ [13ms] - #34 Require(29) node_modules/egg-view-nunjucks/app/extend/application.js
                           ▇ [3ms] - #35 Load extend/request.js
                           ▇ [0ms] - #36 Require(30) node_modules/egg/app/extend/request.js
                           ▇ [3ms] - #37 Load extend/response.js
                           ▇ [1ms] - #38 Require(31) node_modules/egg/app/extend/response.js
                           ▇ [18ms] - #39 Load extend/context.js
                           ▇ [5ms] - #40 Require(32) node_modules/egg-security/app/extend/context.js
                            ▇ [1ms] - #41 Require(33) node_modules/egg-jsonp/app/extend/context.js
                            ▇ [0ms] - #42 Require(34) node_modules/egg-i18n/app/extend/context.js
                            ▇ [8ms] - #43 Require(35) node_modules/egg-multipart/app/extend/context.js
                            ▇ [1ms] - #44 Require(36) node_modules/egg-view/app/extend/context.js
                            ▇ [1ms] - #45 Require(37) node_modules/egg/app/extend/context.js
                             ▇ [10ms] - #46 Load extend/helper.js
                             ▇ [8ms] - #47 Require(38) node_modules/egg-security/app/extend/helper.js
                             ▇ [0ms] - #48 Require(39) node_modules/egg/app/extend/helper.js
                             ▇ [17ms] - #49 Load app.js
                             ▇ [0ms] - #50 Require(40) node_modules/egg-session/app.js
                             ▇ [0ms] - #51 Require(41) node_modules/egg-security/app.js
                             ▇ [4ms] - #52 Require(42) node_modules/egg-onerror/app.js
                             ▇ [5ms] - #53 Require(43) node_modules/egg-i18n/app.js
                              ▇ [3ms] - #54 Require(44) node_modules/egg-watcher/app.js
                              ▇ [1ms] - #55 Require(45) node_modules/egg-schedule/app.js
                              ▇ [0ms] - #56 Require(46) node_modules/egg-multipart/app.js
                              ▇ [1ms] - #57 Require(47) node_modules/egg-development/app.js
                              ▇ [0ms] - #58 Require(48) node_modules/egg-logrotator/app.js
                              ▇ [0ms] - #59 Require(49) node_modules/egg-static/app.js
                              ▇ [1ms] - #60 Require(50) node_modules/egg-sequelize/app.js
                              ▇ [0ms] - #61 Require(51) node_modules/egg-cors/app.js
                              ▇ [0ms] - #62 Require(52) node_modules/egg-view-nunjucks/app.js
                              ▇ [1ms] - #63 Require(53) app.js
                               ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [279ms] - #64 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                           ▇ [7ms] - #65 Load "Symbol(model)" to Application
                                            ▇▇▇▇▇ [85ms] - #66 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
                                            ▇▇▇ [62ms] - #67 Did Load in app.js:didLoad
                                            ▇ [4ms] - #68 Load Service
                                            ▇ [4ms] - #69 Load "service" to Context
                                            ▇▇ [44ms] - #70 Load Middleware
                                            ▇▇ [39ms] - #71 Load "middlewares" to Application
                                               ▇ [3ms] - #72 Load Controller
                                               ▇ [3ms] - #73 Load "controller" to Application
                                               ▇ [1ms] - #74 Load Router
                                               ▇ [0ms] - #75 Require(54) app/router.js
                                               ▇ [11ms] - #76 Before Start in node_modules/egg-core/lib/egg.js:328:10
                                                 ▇ [0ms] - #77 Will Ready in app.js:willReady
2025-08-04 19:59:41,203 INFO 12312 [egg:core] dump config after ready, 3ms
2025-08-04 19:59:48,872 INFO 18400 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\logs\\plan-map-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"plan-map-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"application","localStorage":{"enabled":false}}
2025-08-04 19:59:48,872 INFO 18400 [egg-multipart] stream mode enable
2025-08-04 19:59:49,150 INFO 18400 [egg-static] starting static serve / -> C:\Users\<USER>\Desktop\H5地图规划\后端\view
2025-08-04 19:59:49,151 INFO 18400 [egg-security] use methodnoallow middleware
2025-08-04 19:59:49,152 INFO 18400 [egg-security] use noopen middleware
2025-08-04 19:59:49,153 INFO 18400 [egg-security] use nosniff middleware
2025-08-04 19:59:49,153 INFO 18400 [egg-security] use xssProtection middleware
2025-08-04 19:59:49,153 INFO 18400 [egg-security] use xframe middleware
2025-08-04 19:59:49,154 INFO 18400 [egg-security] use dta middleware
2025-08-04 19:59:49,154 INFO 18400 [egg-security] compose 6 middlewares into one security middleware
2025-08-04 19:59:49,162 INFO 18400 [egg:core] dump config after load, 4ms
2025-08-04 19:59:49,176 INFO 18400 [egg-watcher:application] watcher start success
2025-08-04 19:59:49,196 INFO 18400 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [392ms] - #0 Process Start
                       ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [422ms] - #1 Application Start
                        ▇ [8ms] - #2 Load Plugin
                        ▇ [12ms] - #3 Load Config
                         ▇ [0ms] - #4 Require(0) config/config.default.js
                         ▇ [0ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                         ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                         ▇ [0ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                         ▇ [0ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                         ▇ [0ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                         ▇ [0ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                         ▇ [1ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                         ▇ [0ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                         ▇ [0ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                         ▇ [0ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                         ▇ [0ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                         ▇ [0ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                         ▇ [0ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                         ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                         ▇ [1ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                         ▇ [0ms] - #20 Require(16) node_modules/egg/config/config.default.js
                         ▇ [0ms] - #21 Require(17) config/config.default.js
                         ▇ [0ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                         ▇ [1ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                         ▇ [0ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                         ▇ [1ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                         ▇ [0ms] - #26 Require(22) node_modules/egg/config/config.local.js
                         ▇ [21ms] - #27 Load extend/application.js
                         ▇ [0ms] - #28 Require(23) node_modules/egg-session/app/extend/application.js
                         ▇ [1ms] - #29 Require(24) node_modules/egg-security/app/extend/application.js
                         ▇ [2ms] - #30 Require(25) node_modules/egg-jsonp/app/extend/application.js
                         ▇ [2ms] - #31 Require(26) node_modules/egg-schedule/app/extend/application.js
                          ▇ [1ms] - #32 Require(27) node_modules/egg-logrotator/app/extend/application.js
                          ▇ [0ms] - #33 Require(28) node_modules/egg-view/app/extend/application.js
                          ▇ [13ms] - #34 Require(29) node_modules/egg-view-nunjucks/app/extend/application.js
                           ▇ [3ms] - #35 Load extend/request.js
                           ▇ [0ms] - #36 Require(30) node_modules/egg/app/extend/request.js
                           ▇ [3ms] - #37 Load extend/response.js
                           ▇ [1ms] - #38 Require(31) node_modules/egg/app/extend/response.js
                           ▇ [17ms] - #39 Load extend/context.js
                           ▇ [4ms] - #40 Require(32) node_modules/egg-security/app/extend/context.js
                           ▇ [0ms] - #41 Require(33) node_modules/egg-jsonp/app/extend/context.js
                           ▇ [0ms] - #42 Require(34) node_modules/egg-i18n/app/extend/context.js
                           ▇ [8ms] - #43 Require(35) node_modules/egg-multipart/app/extend/context.js
                            ▇ [0ms] - #44 Require(36) node_modules/egg-view/app/extend/context.js
                            ▇ [1ms] - #45 Require(37) node_modules/egg/app/extend/context.js
                            ▇ [11ms] - #46 Load extend/helper.js
                            ▇ [8ms] - #47 Require(38) node_modules/egg-security/app/extend/helper.js
                             ▇ [0ms] - #48 Require(39) node_modules/egg/app/extend/helper.js
                             ▇ [17ms] - #49 Load app.js
                             ▇ [0ms] - #50 Require(40) node_modules/egg-session/app.js
                             ▇ [0ms] - #51 Require(41) node_modules/egg-security/app.js
                             ▇ [4ms] - #52 Require(42) node_modules/egg-onerror/app.js
                             ▇ [5ms] - #53 Require(43) node_modules/egg-i18n/app.js
                             ▇ [3ms] - #54 Require(44) node_modules/egg-watcher/app.js
                             ▇ [1ms] - #55 Require(45) node_modules/egg-schedule/app.js
                             ▇ [0ms] - #56 Require(46) node_modules/egg-multipart/app.js
                             ▇ [1ms] - #57 Require(47) node_modules/egg-development/app.js
                              ▇ [0ms] - #58 Require(48) node_modules/egg-logrotator/app.js
                              ▇ [0ms] - #59 Require(49) node_modules/egg-static/app.js
                              ▇ [1ms] - #60 Require(50) node_modules/egg-sequelize/app.js
                              ▇ [0ms] - #61 Require(51) node_modules/egg-cors/app.js
                              ▇ [0ms] - #62 Require(52) node_modules/egg-view-nunjucks/app.js
                              ▇ [0ms] - #63 Require(53) app.js
                              ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [301ms] - #64 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                           ▇ [8ms] - #65 Load "Symbol(model)" to Application
                                            ▇▇▇▇▇ [93ms] - #66 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
                                            ▇▇▇▇ [70ms] - #67 Did Load in app.js:didLoad
                                            ▇ [4ms] - #68 Load Service
                                            ▇ [4ms] - #69 Load "service" to Context
                                            ▇▇▇ [51ms] - #70 Load Middleware
                                            ▇▇ [46ms] - #71 Load "middlewares" to Application
                                               ▇ [3ms] - #72 Load Controller
                                               ▇ [3ms] - #73 Load "controller" to Application
                                               ▇ [1ms] - #74 Load Router
                                               ▇ [0ms] - #75 Require(54) app/router.js
                                               ▇ [12ms] - #76 Before Start in node_modules/egg-core/lib/egg.js:328:10
                                                 ▇ [0ms] - #77 Will Ready in app.js:willReady
2025-08-04 19:59:49,196 INFO 18400 [egg:core] dump config after ready, 4ms
2025-08-04 20:00:53,786 INFO 20220 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\logs\\plan-map-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"plan-map-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"application","localStorage":{"enabled":false}}
2025-08-04 20:00:53,786 INFO 20220 [egg-multipart] stream mode enable
2025-08-04 20:00:54,058 INFO 20220 [egg-static] starting static serve / -> C:\Users\<USER>\Desktop\H5地图规划\后端\dist
2025-08-04 20:00:54,060 INFO 20220 [egg-security] use methodnoallow middleware
2025-08-04 20:00:54,060 INFO 20220 [egg-security] use noopen middleware
2025-08-04 20:00:54,061 INFO 20220 [egg-security] use nosniff middleware
2025-08-04 20:00:54,062 INFO 20220 [egg-security] use xssProtection middleware
2025-08-04 20:00:54,062 INFO 20220 [egg-security] use xframe middleware
2025-08-04 20:00:54,062 INFO 20220 [egg-security] use dta middleware
2025-08-04 20:00:54,062 INFO 20220 [egg-security] compose 6 middlewares into one security middleware
2025-08-04 20:00:54,071 INFO 20220 [egg:core] dump config after load, 4ms
2025-08-04 20:00:54,084 INFO 20220 [egg-watcher:application] watcher start success
2025-08-04 20:00:54,105 INFO 20220 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [412ms] - #0 Process Start
                        ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [421ms] - #1 Application Start
                         ▇ [9ms] - #2 Load Plugin
                         ▇ [12ms] - #3 Load Config
                         ▇ [1ms] - #4 Require(0) config/config.default.js
                         ▇ [0ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                         ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                         ▇ [0ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                         ▇ [0ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                         ▇ [0ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                         ▇ [0ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                         ▇ [0ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                         ▇ [1ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                         ▇ [0ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                         ▇ [1ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                          ▇ [0ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                          ▇ [0ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                          ▇ [0ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                          ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                          ▇ [0ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                          ▇ [0ms] - #20 Require(16) node_modules/egg/config/config.default.js
                          ▇ [0ms] - #21 Require(17) config/config.default.js
                          ▇ [0ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                          ▇ [1ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                          ▇ [0ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                          ▇ [1ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                          ▇ [0ms] - #26 Require(22) node_modules/egg/config/config.local.js
                          ▇ [22ms] - #27 Load extend/application.js
                          ▇ [0ms] - #28 Require(23) node_modules/egg-session/app/extend/application.js
                          ▇ [1ms] - #29 Require(24) node_modules/egg-security/app/extend/application.js
                          ▇ [2ms] - #30 Require(25) node_modules/egg-jsonp/app/extend/application.js
                          ▇ [2ms] - #31 Require(26) node_modules/egg-schedule/app/extend/application.js
                          ▇ [1ms] - #32 Require(27) node_modules/egg-logrotator/app/extend/application.js
                          ▇ [1ms] - #33 Require(28) node_modules/egg-view/app/extend/application.js
                          ▇ [13ms] - #34 Require(29) node_modules/egg-view-nunjucks/app/extend/application.js
                           ▇ [3ms] - #35 Load extend/request.js
                           ▇ [1ms] - #36 Require(30) node_modules/egg/app/extend/request.js
                           ▇ [3ms] - #37 Load extend/response.js
                           ▇ [1ms] - #38 Require(31) node_modules/egg/app/extend/response.js
                            ▇ [18ms] - #39 Load extend/context.js
                            ▇ [5ms] - #40 Require(32) node_modules/egg-security/app/extend/context.js
                            ▇ [1ms] - #41 Require(33) node_modules/egg-jsonp/app/extend/context.js
                            ▇ [0ms] - #42 Require(34) node_modules/egg-i18n/app/extend/context.js
                            ▇ [8ms] - #43 Require(35) node_modules/egg-multipart/app/extend/context.js
                             ▇ [0ms] - #44 Require(36) node_modules/egg-view/app/extend/context.js
                             ▇ [0ms] - #45 Require(37) node_modules/egg/app/extend/context.js
                             ▇ [10ms] - #46 Load extend/helper.js
                             ▇ [8ms] - #47 Require(38) node_modules/egg-security/app/extend/helper.js
                             ▇ [1ms] - #48 Require(39) node_modules/egg/app/extend/helper.js
                             ▇ [17ms] - #49 Load app.js
                             ▇ [0ms] - #50 Require(40) node_modules/egg-session/app.js
                             ▇ [1ms] - #51 Require(41) node_modules/egg-security/app.js
                             ▇ [4ms] - #52 Require(42) node_modules/egg-onerror/app.js
                              ▇ [5ms] - #53 Require(43) node_modules/egg-i18n/app.js
                              ▇ [3ms] - #54 Require(44) node_modules/egg-watcher/app.js
                              ▇ [1ms] - #55 Require(45) node_modules/egg-schedule/app.js
                              ▇ [0ms] - #56 Require(46) node_modules/egg-multipart/app.js
                              ▇ [0ms] - #57 Require(47) node_modules/egg-development/app.js
                              ▇ [0ms] - #58 Require(48) node_modules/egg-logrotator/app.js
                              ▇ [0ms] - #59 Require(49) node_modules/egg-static/app.js
                              ▇ [0ms] - #60 Require(50) node_modules/egg-sequelize/app.js
                              ▇ [0ms] - #61 Require(51) node_modules/egg-cors/app.js
                              ▇ [0ms] - #62 Require(52) node_modules/egg-view-nunjucks/app.js
                              ▇ [0ms] - #63 Require(53) app.js
                               ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [295ms] - #64 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                           ▇ [8ms] - #65 Load "Symbol(model)" to Application
                                            ▇▇▇▇▇ [89ms] - #66 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
                                            ▇▇▇ [66ms] - #67 Did Load in app.js:didLoad
                                            ▇ [4ms] - #68 Load Service
                                            ▇ [3ms] - #69 Load "service" to Context
                                            ▇▇ [47ms] - #70 Load Middleware
                                            ▇▇ [41ms] - #71 Load "middlewares" to Application
                                               ▇ [2ms] - #72 Load Controller
                                               ▇ [2ms] - #73 Load "controller" to Application
                                               ▇ [2ms] - #74 Load Router
                                               ▇ [0ms] - #75 Require(54) app/router.js
                                               ▇ [12ms] - #76 Before Start in node_modules/egg-core/lib/egg.js:328:10
                                                 ▇ [0ms] - #77 Will Ready in app.js:willReady
2025-08-04 20:00:54,105 INFO 20220 [egg:core] dump config after ready, 3ms
2025-08-04 20:01:01,341 INFO 22328 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\logs\\plan-map-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"plan-map-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"application","localStorage":{"enabled":false}}
2025-08-04 20:01:01,341 INFO 22328 [egg-multipart] stream mode enable
2025-08-04 20:01:01,616 INFO 22328 [egg-static] starting static serve / -> C:\Users\<USER>\Desktop\H5地图规划\后端\dist
2025-08-04 20:01:01,617 INFO 22328 [egg-security] use methodnoallow middleware
2025-08-04 20:01:01,617 INFO 22328 [egg-security] use noopen middleware
2025-08-04 20:01:01,618 INFO 22328 [egg-security] use nosniff middleware
2025-08-04 20:01:01,619 INFO 22328 [egg-security] use xssProtection middleware
2025-08-04 20:01:01,619 INFO 22328 [egg-security] use xframe middleware
2025-08-04 20:01:01,619 INFO 22328 [egg-security] use dta middleware
2025-08-04 20:01:01,619 INFO 22328 [egg-security] compose 6 middlewares into one security middleware
2025-08-04 20:01:01,627 INFO 22328 [egg:core] dump config after load, 3ms
2025-08-04 20:01:01,649 INFO 22328 [egg-watcher:application] watcher start success
2025-08-04 20:01:01,662 INFO 22328 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [373ms] - #0 Process Start
                       ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [422ms] - #1 Application Start
                       ▇ [8ms] - #2 Load Plugin
                        ▇ [12ms] - #3 Load Config
                        ▇ [0ms] - #4 Require(0) config/config.default.js
                        ▇ [0ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                        ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                        ▇ [0ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                        ▇ [0ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                        ▇ [0ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                        ▇ [0ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                        ▇ [0ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                        ▇ [0ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                        ▇ [1ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                        ▇ [0ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                        ▇ [0ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                        ▇ [0ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                        ▇ [0ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                        ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                        ▇ [0ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                        ▇ [0ms] - #20 Require(16) node_modules/egg/config/config.default.js
                         ▇ [0ms] - #21 Require(17) config/config.default.js
                         ▇ [0ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                         ▇ [0ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                         ▇ [0ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                         ▇ [0ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                         ▇ [0ms] - #26 Require(22) node_modules/egg/config/config.local.js
                         ▇ [22ms] - #27 Load extend/application.js
                         ▇ [1ms] - #28 Require(23) node_modules/egg-session/app/extend/application.js
                         ▇ [0ms] - #29 Require(24) node_modules/egg-security/app/extend/application.js
                         ▇ [1ms] - #30 Require(25) node_modules/egg-jsonp/app/extend/application.js
                         ▇ [1ms] - #31 Require(26) node_modules/egg-schedule/app/extend/application.js
                         ▇ [0ms] - #32 Require(27) node_modules/egg-logrotator/app/extend/application.js
                         ▇ [1ms] - #33 Require(28) node_modules/egg-view/app/extend/application.js
                         ▇ [13ms] - #34 Require(29) node_modules/egg-view-nunjucks/app/extend/application.js
                          ▇ [3ms] - #35 Load extend/request.js
                          ▇ [0ms] - #36 Require(30) node_modules/egg/app/extend/request.js
                          ▇ [3ms] - #37 Load extend/response.js
                          ▇ [0ms] - #38 Require(31) node_modules/egg/app/extend/response.js
                          ▇ [19ms] - #39 Load extend/context.js
                          ▇ [5ms] - #40 Require(32) node_modules/egg-security/app/extend/context.js
                           ▇ [1ms] - #41 Require(33) node_modules/egg-jsonp/app/extend/context.js
                           ▇ [0ms] - #42 Require(34) node_modules/egg-i18n/app/extend/context.js
                           ▇ [10ms] - #43 Require(35) node_modules/egg-multipart/app/extend/context.js
                           ▇ [1ms] - #44 Require(36) node_modules/egg-view/app/extend/context.js
                            ▇ [1ms] - #45 Require(37) node_modules/egg/app/extend/context.js
                            ▇ [10ms] - #46 Load extend/helper.js
                            ▇ [8ms] - #47 Require(38) node_modules/egg-security/app/extend/helper.js
                            ▇ [0ms] - #48 Require(39) node_modules/egg/app/extend/helper.js
                            ▇ [17ms] - #49 Load app.js
                            ▇ [1ms] - #50 Require(40) node_modules/egg-session/app.js
                            ▇ [0ms] - #51 Require(41) node_modules/egg-security/app.js
                            ▇ [4ms] - #52 Require(42) node_modules/egg-onerror/app.js
                             ▇ [4ms] - #53 Require(43) node_modules/egg-i18n/app.js
                             ▇ [4ms] - #54 Require(44) node_modules/egg-watcher/app.js
                             ▇ [0ms] - #55 Require(45) node_modules/egg-schedule/app.js
                             ▇ [1ms] - #56 Require(46) node_modules/egg-multipart/app.js
                             ▇ [0ms] - #57 Require(47) node_modules/egg-development/app.js
                             ▇ [0ms] - #58 Require(48) node_modules/egg-logrotator/app.js
                             ▇ [1ms] - #59 Require(49) node_modules/egg-static/app.js
                             ▇ [0ms] - #60 Require(50) node_modules/egg-sequelize/app.js
                             ▇ [0ms] - #61 Require(51) node_modules/egg-cors/app.js
                             ▇ [1ms] - #62 Require(52) node_modules/egg-view-nunjucks/app.js
                             ▇ [0ms] - #63 Require(53) app.js
                              ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [305ms] - #64 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                           ▇ [8ms] - #65 Load "Symbol(model)" to Application
                                            ▇▇▇▇▇ [88ms] - #66 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
                                            ▇▇▇▇ [65ms] - #67 Did Load in app.js:didLoad
                                            ▇ [4ms] - #68 Load Service
                                            ▇ [4ms] - #69 Load "service" to Context
                                            ▇▇ [46ms] - #70 Load Middleware
                                            ▇▇ [40ms] - #71 Load "middlewares" to Application
                                               ▇ [2ms] - #72 Load Controller
                                               ▇ [2ms] - #73 Load "controller" to Application
                                               ▇ [2ms] - #74 Load Router
                                               ▇ [1ms] - #75 Require(54) app/router.js
                                               ▇ [12ms] - #76 Before Start in node_modules/egg-core/lib/egg.js:328:10
                                                 ▇ [1ms] - #77 Will Ready in app.js:willReady
2025-08-04 20:01:01,662 INFO 22328 [egg:core] dump config after ready, 3ms
2025-08-04 20:01:30,120 INFO 2652 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\logs\\plan-map-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"plan-map-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"application","localStorage":{"enabled":false}}
2025-08-04 20:01:30,120 INFO 2652 [egg-multipart] stream mode enable
2025-08-04 20:01:30,373 INFO 2652 [egg-static] starting static serve / -> C:\Users\<USER>\Desktop\H5地图规划\后端\dist
2025-08-04 20:01:30,374 INFO 2652 [egg-security] use methodnoallow middleware
2025-08-04 20:01:30,375 INFO 2652 [egg-security] use noopen middleware
2025-08-04 20:01:30,376 INFO 2652 [egg-security] use nosniff middleware
2025-08-04 20:01:30,376 INFO 2652 [egg-security] use xssProtection middleware
2025-08-04 20:01:30,376 INFO 2652 [egg-security] use xframe middleware
2025-08-04 20:01:30,377 INFO 2652 [egg-security] use dta middleware
2025-08-04 20:01:30,377 INFO 2652 [egg-security] compose 6 middlewares into one security middleware
2025-08-04 20:01:30,385 INFO 2652 [egg:core] dump config after load, 3ms
2025-08-04 20:01:30,398 INFO 2652 [egg-watcher:application] watcher start success
2025-08-04 20:01:30,416 INFO 2652 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [379ms] - #0 Process Start
                        ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [394ms] - #1 Application Start
                        ▇ [9ms] - #2 Load Plugin
                         ▇ [11ms] - #3 Load Config
                         ▇ [0ms] - #4 Require(0) config/config.default.js
                         ▇ [0ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                         ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                         ▇ [0ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                         ▇ [0ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                         ▇ [1ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                         ▇ [0ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                         ▇ [0ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                         ▇ [0ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                         ▇ [0ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                         ▇ [1ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                         ▇ [0ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                         ▇ [0ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                         ▇ [0ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                         ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                         ▇ [0ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                         ▇ [0ms] - #20 Require(16) node_modules/egg/config/config.default.js
                         ▇ [0ms] - #21 Require(17) config/config.default.js
                          ▇ [0ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                          ▇ [0ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                          ▇ [0ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                          ▇ [0ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                          ▇ [0ms] - #26 Require(22) node_modules/egg/config/config.local.js
                          ▇ [21ms] - #27 Load extend/application.js
                          ▇ [0ms] - #28 Require(23) node_modules/egg-session/app/extend/application.js
                          ▇ [1ms] - #29 Require(24) node_modules/egg-security/app/extend/application.js
                          ▇ [1ms] - #30 Require(25) node_modules/egg-jsonp/app/extend/application.js
                          ▇ [1ms] - #31 Require(26) node_modules/egg-schedule/app/extend/application.js
                          ▇ [0ms] - #32 Require(27) node_modules/egg-logrotator/app/extend/application.js
                          ▇ [0ms] - #33 Require(28) node_modules/egg-view/app/extend/application.js
                          ▇ [13ms] - #34 Require(29) node_modules/egg-view-nunjucks/app/extend/application.js
                           ▇ [3ms] - #35 Load extend/request.js
                           ▇ [0ms] - #36 Require(30) node_modules/egg/app/extend/request.js
                           ▇ [2ms] - #37 Load extend/response.js
                           ▇ [0ms] - #38 Require(31) node_modules/egg/app/extend/response.js
                           ▇ [17ms] - #39 Load extend/context.js
                           ▇ [4ms] - #40 Require(32) node_modules/egg-security/app/extend/context.js
                            ▇ [0ms] - #41 Require(33) node_modules/egg-jsonp/app/extend/context.js
                            ▇ [1ms] - #42 Require(34) node_modules/egg-i18n/app/extend/context.js
                            ▇ [8ms] - #43 Require(35) node_modules/egg-multipart/app/extend/context.js
                            ▇ [1ms] - #44 Require(36) node_modules/egg-view/app/extend/context.js
                            ▇ [1ms] - #45 Require(37) node_modules/egg/app/extend/context.js
                             ▇ [10ms] - #46 Load extend/helper.js
                             ▇ [8ms] - #47 Require(38) node_modules/egg-security/app/extend/helper.js
                             ▇ [0ms] - #48 Require(39) node_modules/egg/app/extend/helper.js
                             ▇ [17ms] - #49 Load app.js
                             ▇ [0ms] - #50 Require(40) node_modules/egg-session/app.js
                             ▇ [0ms] - #51 Require(41) node_modules/egg-security/app.js
                             ▇ [3ms] - #52 Require(42) node_modules/egg-onerror/app.js
                              ▇ [4ms] - #53 Require(43) node_modules/egg-i18n/app.js
                              ▇ [3ms] - #54 Require(44) node_modules/egg-watcher/app.js
                              ▇ [1ms] - #55 Require(45) node_modules/egg-schedule/app.js
                              ▇ [0ms] - #56 Require(46) node_modules/egg-multipart/app.js
                              ▇ [0ms] - #57 Require(47) node_modules/egg-development/app.js
                              ▇ [0ms] - #58 Require(48) node_modules/egg-logrotator/app.js
                              ▇ [0ms] - #59 Require(49) node_modules/egg-static/app.js
                              ▇ [1ms] - #60 Require(50) node_modules/egg-sequelize/app.js
                              ▇ [0ms] - #61 Require(51) node_modules/egg-cors/app.js
                              ▇ [0ms] - #62 Require(52) node_modules/egg-view-nunjucks/app.js
                              ▇ [1ms] - #63 Require(53) app.js
                               ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [275ms] - #64 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                           ▇ [8ms] - #65 Load "Symbol(model)" to Application
                                            ▇▇▇▇▇ [83ms] - #66 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
                                            ▇▇▇ [61ms] - #67 Did Load in app.js:didLoad
                                            ▇ [3ms] - #68 Load Service
                                            ▇ [3ms] - #69 Load "service" to Context
                                            ▇▇ [44ms] - #70 Load Middleware
                                            ▇▇ [39ms] - #71 Load "middlewares" to Application
                                               ▇ [3ms] - #72 Load Controller
                                               ▇ [3ms] - #73 Load "controller" to Application
                                               ▇ [1ms] - #74 Load Router
                                               ▇ [0ms] - #75 Require(54) app/router.js
                                               ▇ [11ms] - #76 Before Start in node_modules/egg-core/lib/egg.js:328:10
                                                 ▇ [0ms] - #77 Will Ready in app.js:willReady
2025-08-04 20:01:30,416 INFO 2652 [egg:core] dump config after ready, 3ms
2025-08-04 20:01:33,199 INFO 2652 [-/127.0.0.1/-/5ms GET /] [egg-view-nunjucks] loading templates from ["C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\dist"]
2025-08-04 20:01:41,350 INFO 5280 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\logs\\plan-map-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"plan-map-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"application","localStorage":{"enabled":false}}
2025-08-04 20:01:41,351 INFO 5280 [egg-multipart] stream mode enable
2025-08-04 20:01:41,616 INFO 5280 [egg-static] starting static serve / -> C:\Users\<USER>\Desktop\H5地图规划\后端\view
2025-08-04 20:01:41,618 INFO 5280 [egg-security] use methodnoallow middleware
2025-08-04 20:01:41,618 INFO 5280 [egg-security] use noopen middleware
2025-08-04 20:01:41,619 INFO 5280 [egg-security] use nosniff middleware
2025-08-04 20:01:41,619 INFO 5280 [egg-security] use xssProtection middleware
2025-08-04 20:01:41,620 INFO 5280 [egg-security] use xframe middleware
2025-08-04 20:01:41,620 INFO 5280 [egg-security] use dta middleware
2025-08-04 20:01:41,620 INFO 5280 [egg-security] compose 6 middlewares into one security middleware
2025-08-04 20:01:41,628 INFO 5280 [egg:core] dump config after load, 4ms
2025-08-04 20:01:41,641 INFO 5280 [egg-watcher:application] watcher start success
2025-08-04 20:01:41,661 INFO 5280 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [385ms] - #0 Process Start
                        ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [410ms] - #1 Application Start
                        ▇ [8ms] - #2 Load Plugin
                         ▇ [12ms] - #3 Load Config
                         ▇ [1ms] - #4 Require(0) config/config.default.js
                         ▇ [1ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                         ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                         ▇ [1ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                         ▇ [0ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                         ▇ [0ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                         ▇ [0ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                         ▇ [0ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                         ▇ [0ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                         ▇ [0ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                         ▇ [1ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                         ▇ [0ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                         ▇ [0ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                         ▇ [0ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                         ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                         ▇ [0ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                         ▇ [0ms] - #20 Require(16) node_modules/egg/config/config.default.js
                         ▇ [0ms] - #21 Require(17) config/config.default.js
                         ▇ [0ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                         ▇ [0ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                         ▇ [0ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                         ▇ [0ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                         ▇ [0ms] - #26 Require(22) node_modules/egg/config/config.local.js
                         ▇ [21ms] - #27 Load extend/application.js
                         ▇ [0ms] - #28 Require(23) node_modules/egg-session/app/extend/application.js
                         ▇ [1ms] - #29 Require(24) node_modules/egg-security/app/extend/application.js
                          ▇ [1ms] - #30 Require(25) node_modules/egg-jsonp/app/extend/application.js
                          ▇ [2ms] - #31 Require(26) node_modules/egg-schedule/app/extend/application.js
                          ▇ [1ms] - #32 Require(27) node_modules/egg-logrotator/app/extend/application.js
                          ▇ [0ms] - #33 Require(28) node_modules/egg-view/app/extend/application.js
                          ▇ [13ms] - #34 Require(29) node_modules/egg-view-nunjucks/app/extend/application.js
                           ▇ [3ms] - #35 Load extend/request.js
                           ▇ [1ms] - #36 Require(30) node_modules/egg/app/extend/request.js
                           ▇ [3ms] - #37 Load extend/response.js
                           ▇ [1ms] - #38 Require(31) node_modules/egg/app/extend/response.js
                           ▇ [18ms] - #39 Load extend/context.js
                           ▇ [5ms] - #40 Require(32) node_modules/egg-security/app/extend/context.js
                           ▇ [1ms] - #41 Require(33) node_modules/egg-jsonp/app/extend/context.js
                            ▇ [0ms] - #42 Require(34) node_modules/egg-i18n/app/extend/context.js
                            ▇ [9ms] - #43 Require(35) node_modules/egg-multipart/app/extend/context.js
                            ▇ [1ms] - #44 Require(36) node_modules/egg-view/app/extend/context.js
                            ▇ [1ms] - #45 Require(37) node_modules/egg/app/extend/context.js
                            ▇ [10ms] - #46 Load extend/helper.js
                            ▇ [8ms] - #47 Require(38) node_modules/egg-security/app/extend/helper.js
                             ▇ [0ms] - #48 Require(39) node_modules/egg/app/extend/helper.js
                             ▇ [17ms] - #49 Load app.js
                             ▇ [0ms] - #50 Require(40) node_modules/egg-session/app.js
                             ▇ [1ms] - #51 Require(41) node_modules/egg-security/app.js
                             ▇ [4ms] - #52 Require(42) node_modules/egg-onerror/app.js
                             ▇ [4ms] - #53 Require(43) node_modules/egg-i18n/app.js
                              ▇ [4ms] - #54 Require(44) node_modules/egg-watcher/app.js
                              ▇ [0ms] - #55 Require(45) node_modules/egg-schedule/app.js
                              ▇ [1ms] - #56 Require(46) node_modules/egg-multipart/app.js
                              ▇ [0ms] - #57 Require(47) node_modules/egg-development/app.js
                              ▇ [0ms] - #58 Require(48) node_modules/egg-logrotator/app.js
                              ▇ [0ms] - #59 Require(49) node_modules/egg-static/app.js
                              ▇ [0ms] - #60 Require(50) node_modules/egg-sequelize/app.js
                              ▇ [0ms] - #61 Require(51) node_modules/egg-cors/app.js
                              ▇ [0ms] - #62 Require(52) node_modules/egg-view-nunjucks/app.js
                              ▇ [0ms] - #63 Require(53) app.js
                              ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [288ms] - #64 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                           ▇ [8ms] - #65 Load "Symbol(model)" to Application
                                            ▇▇▇▇▇ [85ms] - #66 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
                                            ▇▇▇ [63ms] - #67 Did Load in app.js:didLoad
                                            ▇ [3ms] - #68 Load Service
                                            ▇ [3ms] - #69 Load "service" to Context
                                            ▇▇ [45ms] - #70 Load Middleware
                                            ▇▇ [40ms] - #71 Load "middlewares" to Application
                                               ▇ [3ms] - #72 Load Controller
                                               ▇ [3ms] - #73 Load "controller" to Application
                                               ▇ [1ms] - #74 Load Router
                                               ▇ [0ms] - #75 Require(54) app/router.js
                                               ▇ [12ms] - #76 Before Start in node_modules/egg-core/lib/egg.js:328:10
                                                 ▇ [0ms] - #77 Will Ready in app.js:willReady
2025-08-04 20:01:41,661 INFO 5280 [egg:core] dump config after ready, 3ms
2025-08-04 20:01:55,774 INFO 2076 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\logs\\plan-map-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"plan-map-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"application","localStorage":{"enabled":false}}
2025-08-04 20:01:55,774 INFO 2076 [egg-multipart] stream mode enable
2025-08-04 20:01:56,035 INFO 2076 [egg-static] starting static serve / -> C:\Users\<USER>\Desktop\H5地图规划\后端\view
2025-08-04 20:01:56,036 INFO 2076 [egg-security] use methodnoallow middleware
2025-08-04 20:01:56,037 INFO 2076 [egg-security] use noopen middleware
2025-08-04 20:01:56,038 INFO 2076 [egg-security] use nosniff middleware
2025-08-04 20:01:56,038 INFO 2076 [egg-security] use xssProtection middleware
2025-08-04 20:01:56,039 INFO 2076 [egg-security] use xframe middleware
2025-08-04 20:01:56,039 INFO 2076 [egg-security] use dta middleware
2025-08-04 20:01:56,039 INFO 2076 [egg-security] compose 6 middlewares into one security middleware
2025-08-04 20:01:56,047 INFO 2076 [egg:core] dump config after load, 3ms
2025-08-04 20:01:56,068 INFO 2076 [egg-watcher:application] watcher start success
2025-08-04 20:01:56,081 INFO 2076 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [386ms] - #0 Process Start
                        ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [407ms] - #1 Application Start
                        ▇ [9ms] - #2 Load Plugin
                         ▇ [11ms] - #3 Load Config
                         ▇ [0ms] - #4 Require(0) config/config.default.js
                         ▇ [0ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                         ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                         ▇ [0ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                         ▇ [0ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                         ▇ [0ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                         ▇ [0ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                         ▇ [0ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                         ▇ [0ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                         ▇ [1ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                         ▇ [0ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                         ▇ [0ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                         ▇ [0ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                         ▇ [0ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                         ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                         ▇ [0ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                         ▇ [0ms] - #20 Require(16) node_modules/egg/config/config.default.js
                         ▇ [0ms] - #21 Require(17) config/config.default.js
                         ▇ [0ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                         ▇ [0ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                         ▇ [1ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                         ▇ [0ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                         ▇ [0ms] - #26 Require(22) node_modules/egg/config/config.local.js
                          ▇ [22ms] - #27 Load extend/application.js
                          ▇ [0ms] - #28 Require(23) node_modules/egg-session/app/extend/application.js
                          ▇ [1ms] - #29 Require(24) node_modules/egg-security/app/extend/application.js
                          ▇ [1ms] - #30 Require(25) node_modules/egg-jsonp/app/extend/application.js
                          ▇ [1ms] - #31 Require(26) node_modules/egg-schedule/app/extend/application.js
                          ▇ [0ms] - #32 Require(27) node_modules/egg-logrotator/app/extend/application.js
                          ▇ [0ms] - #33 Require(28) node_modules/egg-view/app/extend/application.js
                          ▇ [13ms] - #34 Require(29) node_modules/egg-view-nunjucks/app/extend/application.js
                           ▇ [3ms] - #35 Load extend/request.js
                           ▇ [0ms] - #36 Require(30) node_modules/egg/app/extend/request.js
                           ▇ [3ms] - #37 Load extend/response.js
                           ▇ [1ms] - #38 Require(31) node_modules/egg/app/extend/response.js
                           ▇ [18ms] - #39 Load extend/context.js
                           ▇ [4ms] - #40 Require(32) node_modules/egg-security/app/extend/context.js
                            ▇ [0ms] - #41 Require(33) node_modules/egg-jsonp/app/extend/context.js
                            ▇ [0ms] - #42 Require(34) node_modules/egg-i18n/app/extend/context.js
                            ▇ [8ms] - #43 Require(35) node_modules/egg-multipart/app/extend/context.js
                            ▇ [0ms] - #44 Require(36) node_modules/egg-view/app/extend/context.js
                            ▇ [0ms] - #45 Require(37) node_modules/egg/app/extend/context.js
                            ▇ [10ms] - #46 Load extend/helper.js
                            ▇ [8ms] - #47 Require(38) node_modules/egg-security/app/extend/helper.js
                             ▇ [0ms] - #48 Require(39) node_modules/egg/app/extend/helper.js
                             ▇ [17ms] - #49 Load app.js
                             ▇ [0ms] - #50 Require(40) node_modules/egg-session/app.js
                             ▇ [0ms] - #51 Require(41) node_modules/egg-security/app.js
                             ▇ [4ms] - #52 Require(42) node_modules/egg-onerror/app.js
                             ▇ [5ms] - #53 Require(43) node_modules/egg-i18n/app.js
                              ▇ [3ms] - #54 Require(44) node_modules/egg-watcher/app.js
                              ▇ [1ms] - #55 Require(45) node_modules/egg-schedule/app.js
                              ▇ [1ms] - #56 Require(46) node_modules/egg-multipart/app.js
                              ▇ [0ms] - #57 Require(47) node_modules/egg-development/app.js
                              ▇ [0ms] - #58 Require(48) node_modules/egg-logrotator/app.js
                              ▇ [0ms] - #59 Require(49) node_modules/egg-static/app.js
                              ▇ [0ms] - #60 Require(50) node_modules/egg-sequelize/app.js
                              ▇ [0ms] - #61 Require(51) node_modules/egg-cors/app.js
                              ▇ [0ms] - #62 Require(52) node_modules/egg-view-nunjucks/app.js
                              ▇ [0ms] - #63 Require(53) app.js
                              ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [291ms] - #64 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                           ▇ [8ms] - #65 Load "Symbol(model)" to Application
                                            ▇▇▇▇▇ [88ms] - #66 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
                                            ▇▇▇▇ [65ms] - #67 Did Load in app.js:didLoad
                                            ▇ [3ms] - #68 Load Service
                                            ▇ [3ms] - #69 Load "service" to Context
                                            ▇▇ [47ms] - #70 Load Middleware
                                            ▇▇ [42ms] - #71 Load "middlewares" to Application
                                               ▇ [3ms] - #72 Load Controller
                                               ▇ [3ms] - #73 Load "controller" to Application
                                               ▇ [2ms] - #74 Load Router
                                               ▇ [0ms] - #75 Require(54) app/router.js
                                               ▇ [12ms] - #76 Before Start in node_modules/egg-core/lib/egg.js:328:10
                                                 ▇ [0ms] - #77 Will Ready in app.js:willReady
2025-08-04 20:01:56,081 INFO 2076 [egg:core] dump config after ready, 3ms
2025-08-04 20:02:20,590 INFO 2076 [-/127.0.0.1/-/1ms GET /] [egg-view-nunjucks] loading templates from ["C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\view"]
2025-08-04 20:02:25,765 INFO 17492 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\logs\\plan-map-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"plan-map-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"application","localStorage":{"enabled":false}}
2025-08-04 20:02:25,765 INFO 17492 [egg-multipart] stream mode enable
2025-08-04 20:02:26,043 INFO 17492 [egg-static] starting static serve / -> C:\Users\<USER>\Desktop\H5地图规划\后端\view
2025-08-04 20:02:26,045 INFO 17492 [egg-security] use methodnoallow middleware
2025-08-04 20:02:26,045 INFO 17492 [egg-security] use noopen middleware
2025-08-04 20:02:26,046 INFO 17492 [egg-security] use nosniff middleware
2025-08-04 20:02:26,046 INFO 17492 [egg-security] use xssProtection middleware
2025-08-04 20:02:26,047 INFO 17492 [egg-security] use xframe middleware
2025-08-04 20:02:26,047 INFO 17492 [egg-security] use dta middleware
2025-08-04 20:02:26,047 INFO 17492 [egg-security] compose 6 middlewares into one security middleware
2025-08-04 20:02:26,055 INFO 17492 [egg:core] dump config after load, 4ms
2025-08-04 20:02:26,068 INFO 17492 [egg-watcher:application] watcher start success
2025-08-04 20:02:26,087 INFO 17492 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [388ms] - #0 Process Start
                       ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [431ms] - #1 Application Start
                        ▇ [11ms] - #2 Load Plugin
                        ▇ [12ms] - #3 Load Config
                        ▇ [0ms] - #4 Require(0) config/config.default.js
                        ▇ [1ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                        ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                        ▇ [0ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                        ▇ [0ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                        ▇ [1ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                         ▇ [0ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                         ▇ [0ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                         ▇ [0ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                         ▇ [0ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                         ▇ [0ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                         ▇ [0ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                         ▇ [0ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                         ▇ [0ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                         ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                         ▇ [0ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                         ▇ [0ms] - #20 Require(16) node_modules/egg/config/config.default.js
                         ▇ [0ms] - #21 Require(17) config/config.default.js
                         ▇ [0ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                         ▇ [0ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                         ▇ [0ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                         ▇ [0ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                         ▇ [0ms] - #26 Require(22) node_modules/egg/config/config.local.js
                         ▇ [22ms] - #27 Load extend/application.js
                         ▇ [1ms] - #28 Require(23) node_modules/egg-session/app/extend/application.js
                         ▇ [0ms] - #29 Require(24) node_modules/egg-security/app/extend/application.js
                         ▇ [1ms] - #30 Require(25) node_modules/egg-jsonp/app/extend/application.js
                         ▇ [1ms] - #31 Require(26) node_modules/egg-schedule/app/extend/application.js
                         ▇ [0ms] - #32 Require(27) node_modules/egg-logrotator/app/extend/application.js
                         ▇ [1ms] - #33 Require(28) node_modules/egg-view/app/extend/application.js
                         ▇ [14ms] - #34 Require(29) node_modules/egg-view-nunjucks/app/extend/application.js
                          ▇ [3ms] - #35 Load extend/request.js
                          ▇ [1ms] - #36 Require(30) node_modules/egg/app/extend/request.js
                           ▇ [3ms] - #37 Load extend/response.js
                           ▇ [1ms] - #38 Require(31) node_modules/egg/app/extend/response.js
                           ▇ [22ms] - #39 Load extend/context.js
                           ▇ [5ms] - #40 Require(32) node_modules/egg-security/app/extend/context.js
                           ▇ [1ms] - #41 Require(33) node_modules/egg-jsonp/app/extend/context.js
                           ▇ [0ms] - #42 Require(34) node_modules/egg-i18n/app/extend/context.js
                           ▇ [10ms] - #43 Require(35) node_modules/egg-multipart/app/extend/context.js
                            ▇ [0ms] - #44 Require(36) node_modules/egg-view/app/extend/context.js
                            ▇ [0ms] - #45 Require(37) node_modules/egg/app/extend/context.js
                            ▇ [11ms] - #46 Load extend/helper.js
                            ▇ [8ms] - #47 Require(38) node_modules/egg-security/app/extend/helper.js
                             ▇ [1ms] - #48 Require(39) node_modules/egg/app/extend/helper.js
                             ▇ [18ms] - #49 Load app.js
                             ▇ [0ms] - #50 Require(40) node_modules/egg-session/app.js
                             ▇ [1ms] - #51 Require(41) node_modules/egg-security/app.js
                             ▇ [4ms] - #52 Require(42) node_modules/egg-onerror/app.js
                             ▇ [4ms] - #53 Require(43) node_modules/egg-i18n/app.js
                             ▇ [3ms] - #54 Require(44) node_modules/egg-watcher/app.js
                              ▇ [0ms] - #55 Require(45) node_modules/egg-schedule/app.js
                              ▇ [0ms] - #56 Require(46) node_modules/egg-multipart/app.js
                              ▇ [1ms] - #57 Require(47) node_modules/egg-development/app.js
                              ▇ [0ms] - #58 Require(48) node_modules/egg-logrotator/app.js
                              ▇ [0ms] - #59 Require(49) node_modules/egg-static/app.js
                              ▇ [1ms] - #60 Require(50) node_modules/egg-sequelize/app.js
                              ▇ [0ms] - #61 Require(51) node_modules/egg-cors/app.js
                              ▇ [0ms] - #62 Require(52) node_modules/egg-view-nunjucks/app.js
                              ▇ [1ms] - #63 Require(53) app.js
                              ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [300ms] - #64 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                           ▇ [8ms] - #65 Load "Symbol(model)" to Application
                                            ▇▇▇▇▇ [88ms] - #66 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
                                            ▇▇▇▇ [66ms] - #67 Did Load in app.js:didLoad
                                            ▇ [4ms] - #68 Load Service
                                            ▇ [4ms] - #69 Load "service" to Context
                                            ▇▇ [47ms] - #70 Load Middleware
                                            ▇▇ [42ms] - #71 Load "middlewares" to Application
                                               ▇ [3ms] - #72 Load Controller
                                               ▇ [3ms] - #73 Load "controller" to Application
                                               ▇ [1ms] - #74 Load Router
                                               ▇ [0ms] - #75 Require(54) app/router.js
                                               ▇ [12ms] - #76 Before Start in node_modules/egg-core/lib/egg.js:328:10
                                                 ▇ [0ms] - #77 Will Ready in app.js:willReady
2025-08-04 20:02:26,087 INFO 17492 [egg:core] dump config after ready, 3ms
2025-08-21 23:37:46,020 INFO 33808 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\logs\\plan-map-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"plan-map-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"application","localStorage":{"enabled":false}}
2025-08-21 23:37:46,020 INFO 33808 [egg-multipart] file mode enable
2025-08-21 23:37:46,020 INFO 33808 [egg-multipart] will save temporary files to "C:\\Users\\<USER>\\AppData\\Local\\Temp\\egg-multipart-tmp\\plan-map-node", cleanup job cron: "0 30 4 * * *"
2025-08-21 23:37:46,338 INFO 33808 [egg-static] starting static serve /uploads -> C:\Users\<USER>\Desktop\H5地图规划\后端\uploads
2025-08-21 23:37:46,340 INFO 33808 [egg-security] use methodnoallow middleware
2025-08-21 23:37:46,340 INFO 33808 [egg-security] use noopen middleware
2025-08-21 23:37:46,343 INFO 33808 [egg-security] use nosniff middleware
2025-08-21 23:37:46,343 INFO 33808 [egg-security] use xssProtection middleware
2025-08-21 23:37:46,344 INFO 33808 [egg-security] use xframe middleware
2025-08-21 23:37:46,344 INFO 33808 [egg-security] use dta middleware
2025-08-21 23:37:46,344 INFO 33808 [egg-security] compose 6 middlewares into one security middleware
2025-08-21 23:37:46,354 INFO 33808 [egg:core] dump config after load, 4ms
2025-08-21 23:37:46,377 INFO 33808 [egg-watcher:application] watcher start success
2025-08-21 23:37:46,392 INFO 33808 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [398ms] - #0 Process Start
                     ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [511ms] - #1 Application Start
                      ▇ [10ms] - #2 Load Plugin
                      ▇ [12ms] - #3 Load Config
                      ▇ [0ms] - #4 Require(0) config/config.default.js
                      ▇ [0ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                      ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                       ▇ [0ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                       ▇ [0ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                       ▇ [0ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                       ▇ [0ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                       ▇ [0ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                       ▇ [0ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                       ▇ [0ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                       ▇ [0ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                       ▇ [0ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                       ▇ [0ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                       ▇ [0ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                       ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                       ▇ [1ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                       ▇ [0ms] - #20 Require(16) node_modules/egg/config/config.default.js
                       ▇ [0ms] - #21 Require(17) config/config.default.js
                       ▇ [0ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                       ▇ [1ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                       ▇ [0ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                       ▇ [1ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                       ▇ [0ms] - #26 Require(22) node_modules/egg/config/config.local.js
                       ▇ [35ms] - #27 Load extend/application.js
                       ▇ [1ms] - #28 Require(23) node_modules/egg-session/app/extend/application.js
                       ▇ [1ms] - #29 Require(24) node_modules/egg-security/app/extend/application.js
                       ▇ [2ms] - #30 Require(25) node_modules/egg-jsonp/app/extend/application.js
                       ▇ [2ms] - #31 Require(26) node_modules/egg-schedule/app/extend/application.js
                       ▇ [1ms] - #32 Require(27) node_modules/egg-logrotator/app/extend/application.js
                       ▇ [0ms] - #33 Require(28) node_modules/egg-view/app/extend/application.js
                        ▇ [23ms] - #34 Require(29) node_modules/egg-view-nunjucks/app/extend/application.js
                         ▇ [3ms] - #35 Load extend/request.js
                         ▇ [1ms] - #36 Require(30) node_modules/egg/app/extend/request.js
                         ▇ [4ms] - #37 Load extend/response.js
                         ▇ [1ms] - #38 Require(31) node_modules/egg/app/extend/response.js
                         ▇ [18ms] - #39 Load extend/context.js
                         ▇ [5ms] - #40 Require(32) node_modules/egg-security/app/extend/context.js
                          ▇ [1ms] - #41 Require(33) node_modules/egg-jsonp/app/extend/context.js
                          ▇ [0ms] - #42 Require(34) node_modules/egg-i18n/app/extend/context.js
                          ▇ [9ms] - #43 Require(35) node_modules/egg-multipart/app/extend/context.js
                          ▇ [0ms] - #44 Require(36) node_modules/egg-view/app/extend/context.js
                          ▇ [1ms] - #45 Require(37) node_modules/egg/app/extend/context.js
                          ▇ [22ms] - #46 Load extend/helper.js
                          ▇ [15ms] - #47 Require(38) node_modules/egg-security/app/extend/helper.js
                           ▇ [0ms] - #48 Require(39) node_modules/egg/app/extend/helper.js
                            ▇ [28ms] - #49 Load app.js
                            ▇ [0ms] - #50 Require(40) node_modules/egg-session/app.js
                            ▇ [1ms] - #51 Require(41) node_modules/egg-security/app.js
                            ▇ [9ms] - #52 Require(42) node_modules/egg-onerror/app.js
                            ▇ [8ms] - #53 Require(43) node_modules/egg-i18n/app.js
                             ▇ [4ms] - #54 Require(44) node_modules/egg-watcher/app.js
                             ▇ [0ms] - #55 Require(45) node_modules/egg-schedule/app.js
                             ▇ [1ms] - #56 Require(46) node_modules/egg-multipart/app.js
                             ▇ [0ms] - #57 Require(47) node_modules/egg-development/app.js
                             ▇ [0ms] - #58 Require(48) node_modules/egg-logrotator/app.js
                             ▇ [0ms] - #59 Require(49) node_modules/egg-static/app.js
                             ▇ [0ms] - #60 Require(50) node_modules/egg-sequelize/app.js
                             ▇ [1ms] - #61 Require(51) node_modules/egg-cors/app.js
                             ▇ [0ms] - #62 Require(52) node_modules/egg-view-nunjucks/app.js
                             ▇ [0ms] - #63 Require(53) app.js
                             ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [354ms] - #64 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                          ▇ [10ms] - #65 Load "Symbol(model)" to Application
                                          ▇▇▇▇▇▇ [125ms] - #66 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
                                          ▇▇▇▇▇ [98ms] - #67 Did Load in app.js:didLoad
                                          ▇ [4ms] - #68 Load Service
                                          ▇ [4ms] - #69 Load "service" to Context
                                           ▇▇▇▇ [77ms] - #70 Load Middleware
                                           ▇▇▇ [70ms] - #71 Load "middlewares" to Application
                                               ▇ [4ms] - #72 Load Controller
                                               ▇ [4ms] - #73 Load "controller" to Application
                                               ▇ [2ms] - #74 Load Router
                                               ▇ [0ms] - #75 Require(54) app/router.js
                                               ▇ [12ms] - #76 Before Start in node_modules/egg-core/lib/egg.js:328:10
                                                 ▇ [0ms] - #77 Will Ready in app.js:willReady
2025-08-21 23:37:46,392 INFO 33808 [egg:core] dump config after ready, 4ms
2025-08-21 23:51:30,690 INFO 32440 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\logs\\plan-map-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"plan-map-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"application","localStorage":{"enabled":false}}
2025-08-21 23:51:30,691 INFO 32440 [egg-multipart] file mode enable
2025-08-21 23:51:30,691 INFO 32440 [egg-multipart] will save temporary files to "C:\\Users\\<USER>\\AppData\\Local\\Temp\\egg-multipart-tmp\\plan-map-node", cleanup job cron: "0 30 4 * * *"
2025-08-21 23:51:30,955 INFO 32440 [egg-static] starting static serve /uploads -> C:\Users\<USER>\Desktop\H5地图规划\后端\uploads
2025-08-21 23:51:30,956 INFO 32440 [egg-security] use methodnoallow middleware
2025-08-21 23:51:30,956 INFO 32440 [egg-security] use noopen middleware
2025-08-21 23:51:30,958 INFO 32440 [egg-security] use nosniff middleware
2025-08-21 23:51:30,958 INFO 32440 [egg-security] use xssProtection middleware
2025-08-21 23:51:30,958 INFO 32440 [egg-security] use xframe middleware
2025-08-21 23:51:30,959 INFO 32440 [egg-security] use dta middleware
2025-08-21 23:51:30,959 INFO 32440 [egg-security] compose 6 middlewares into one security middleware
2025-08-21 23:51:30,968 INFO 32440 [egg:core] dump config after load, 3ms
2025-08-21 23:51:30,982 INFO 32440 [egg-watcher:application] watcher start success
2025-08-21 23:51:30,999 INFO 32440 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [392ms] - #0 Process Start
                        ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [410ms] - #1 Application Start
                        ▇ [9ms] - #2 Load Plugin
                         ▇ [11ms] - #3 Load Config
                         ▇ [0ms] - #4 Require(0) config/config.default.js
                         ▇ [0ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                         ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                         ▇ [0ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                         ▇ [0ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                         ▇ [0ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                         ▇ [0ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                         ▇ [1ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                         ▇ [0ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                         ▇ [1ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                         ▇ [0ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                         ▇ [0ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                         ▇ [0ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                         ▇ [0ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                         ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                         ▇ [0ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                         ▇ [0ms] - #20 Require(16) node_modules/egg/config/config.default.js
                          ▇ [0ms] - #21 Require(17) config/config.default.js
                          ▇ [0ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                          ▇ [0ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                          ▇ [0ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                          ▇ [0ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                          ▇ [0ms] - #26 Require(22) node_modules/egg/config/config.local.js
                          ▇ [21ms] - #27 Load extend/application.js
                          ▇ [0ms] - #28 Require(23) node_modules/egg-session/app/extend/application.js
                          ▇ [0ms] - #29 Require(24) node_modules/egg-security/app/extend/application.js
                          ▇ [2ms] - #30 Require(25) node_modules/egg-jsonp/app/extend/application.js
                          ▇ [2ms] - #31 Require(26) node_modules/egg-schedule/app/extend/application.js
                          ▇ [1ms] - #32 Require(27) node_modules/egg-logrotator/app/extend/application.js
                          ▇ [1ms] - #33 Require(28) node_modules/egg-view/app/extend/application.js
                          ▇ [13ms] - #34 Require(29) node_modules/egg-view-nunjucks/app/extend/application.js
                           ▇ [3ms] - #35 Load extend/request.js
                           ▇ [0ms] - #36 Require(30) node_modules/egg/app/extend/request.js
                           ▇ [3ms] - #37 Load extend/response.js
                           ▇ [1ms] - #38 Require(31) node_modules/egg/app/extend/response.js
                           ▇ [19ms] - #39 Load extend/context.js
                           ▇ [6ms] - #40 Require(32) node_modules/egg-security/app/extend/context.js
                            ▇ [0ms] - #41 Require(33) node_modules/egg-jsonp/app/extend/context.js
                            ▇ [0ms] - #42 Require(34) node_modules/egg-i18n/app/extend/context.js
                            ▇ [9ms] - #43 Require(35) node_modules/egg-multipart/app/extend/context.js
                            ▇ [1ms] - #44 Require(36) node_modules/egg-view/app/extend/context.js
                            ▇ [1ms] - #45 Require(37) node_modules/egg/app/extend/context.js
                             ▇ [10ms] - #46 Load extend/helper.js
                             ▇ [8ms] - #47 Require(38) node_modules/egg-security/app/extend/helper.js
                             ▇ [0ms] - #48 Require(39) node_modules/egg/app/extend/helper.js
                             ▇ [16ms] - #49 Load app.js
                             ▇ [0ms] - #50 Require(40) node_modules/egg-session/app.js
                             ▇ [1ms] - #51 Require(41) node_modules/egg-security/app.js
                             ▇ [4ms] - #52 Require(42) node_modules/egg-onerror/app.js
                              ▇ [4ms] - #53 Require(43) node_modules/egg-i18n/app.js
                              ▇ [4ms] - #54 Require(44) node_modules/egg-watcher/app.js
                              ▇ [0ms] - #55 Require(45) node_modules/egg-schedule/app.js
                              ▇ [1ms] - #56 Require(46) node_modules/egg-multipart/app.js
                              ▇ [0ms] - #57 Require(47) node_modules/egg-development/app.js
                              ▇ [1ms] - #58 Require(48) node_modules/egg-logrotator/app.js
                              ▇ [0ms] - #59 Require(49) node_modules/egg-static/app.js
                              ▇ [0ms] - #60 Require(50) node_modules/egg-sequelize/app.js
                              ▇ [1ms] - #61 Require(51) node_modules/egg-cors/app.js
                              ▇ [0ms] - #62 Require(52) node_modules/egg-view-nunjucks/app.js
                              ▇ [0ms] - #63 Require(53) app.js
                               ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [289ms] - #64 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                           ▇ [11ms] - #65 Load "Symbol(model)" to Application
                                            ▇▇▇▇▇ [87ms] - #66 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
                                            ▇▇▇▇ [67ms] - #67 Did Load in app.js:didLoad
                                            ▇ [4ms] - #68 Load Service
                                            ▇ [4ms] - #69 Load "service" to Context
                                            ▇▇ [46ms] - #70 Load Middleware
                                            ▇▇ [41ms] - #71 Load "middlewares" to Application
                                               ▇ [4ms] - #72 Load Controller
                                               ▇ [4ms] - #73 Load "controller" to Application
                                               ▇ [2ms] - #74 Load Router
                                               ▇ [0ms] - #75 Require(54) app/router.js
                                               ▇ [13ms] - #76 Before Start in node_modules/egg-core/lib/egg.js:328:10
                                                 ▇ [0ms] - #77 Will Ready in app.js:willReady
2025-08-21 23:51:30,999 INFO 32440 [egg:core] dump config after ready, 3ms
2025-08-21 23:51:31,802 INFO 30536 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\logs\\plan-map-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"plan-map-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"application","localStorage":{"enabled":false}}
2025-08-21 23:51:31,802 INFO 30536 [egg-multipart] file mode enable
2025-08-21 23:51:31,802 INFO 30536 [egg-multipart] will save temporary files to "C:\\Users\\<USER>\\AppData\\Local\\Temp\\egg-multipart-tmp\\plan-map-node", cleanup job cron: "0 30 4 * * *"
2025-08-21 23:51:32,067 INFO 30536 [egg-static] starting static serve /uploads -> C:\Users\<USER>\Desktop\H5地图规划\后端\uploads
2025-08-21 23:51:32,069 INFO 30536 [egg-security] use methodnoallow middleware
2025-08-21 23:51:32,069 INFO 30536 [egg-security] use noopen middleware
2025-08-21 23:51:32,070 INFO 30536 [egg-security] use nosniff middleware
2025-08-21 23:51:32,070 INFO 30536 [egg-security] use xssProtection middleware
2025-08-21 23:51:32,071 INFO 30536 [egg-security] use xframe middleware
2025-08-21 23:51:32,071 INFO 30536 [egg-security] use dta middleware
2025-08-21 23:51:32,071 INFO 30536 [egg-security] compose 6 middlewares into one security middleware
2025-08-21 23:51:32,081 INFO 30536 [egg:core] dump config after load, 4ms
2025-08-21 23:51:32,094 INFO 30536 [egg-watcher:application] watcher start success
2025-08-21 23:51:32,113 INFO 30536 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [382ms] - #0 Process Start
                       ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [411ms] - #1 Application Start
                        ▇ [10ms] - #2 Load Plugin
                         ▇ [12ms] - #3 Load Config
                         ▇ [1ms] - #4 Require(0) config/config.default.js
                         ▇ [0ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                         ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                         ▇ [0ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                         ▇ [0ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                         ▇ [0ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                         ▇ [0ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                         ▇ [1ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                         ▇ [0ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                         ▇ [1ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                         ▇ [0ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                         ▇ [0ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                         ▇ [1ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                         ▇ [0ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                         ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                         ▇ [0ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                         ▇ [0ms] - #20 Require(16) node_modules/egg/config/config.default.js
                         ▇ [0ms] - #21 Require(17) config/config.default.js
                         ▇ [0ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                         ▇ [0ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                         ▇ [0ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                         ▇ [0ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                         ▇ [0ms] - #26 Require(22) node_modules/egg/config/config.local.js
                         ▇ [21ms] - #27 Load extend/application.js
                         ▇ [0ms] - #28 Require(23) node_modules/egg-session/app/extend/application.js
                          ▇ [0ms] - #29 Require(24) node_modules/egg-security/app/extend/application.js
                          ▇ [2ms] - #30 Require(25) node_modules/egg-jsonp/app/extend/application.js
                          ▇ [2ms] - #31 Require(26) node_modules/egg-schedule/app/extend/application.js
                          ▇ [1ms] - #32 Require(27) node_modules/egg-logrotator/app/extend/application.js
                          ▇ [1ms] - #33 Require(28) node_modules/egg-view/app/extend/application.js
                          ▇ [13ms] - #34 Require(29) node_modules/egg-view-nunjucks/app/extend/application.js
                           ▇ [3ms] - #35 Load extend/request.js
                           ▇ [1ms] - #36 Require(30) node_modules/egg/app/extend/request.js
                           ▇ [3ms] - #37 Load extend/response.js
                           ▇ [1ms] - #38 Require(31) node_modules/egg/app/extend/response.js
                           ▇ [18ms] - #39 Load extend/context.js
                           ▇ [5ms] - #40 Require(32) node_modules/egg-security/app/extend/context.js
                           ▇ [1ms] - #41 Require(33) node_modules/egg-jsonp/app/extend/context.js
                            ▇ [0ms] - #42 Require(34) node_modules/egg-i18n/app/extend/context.js
                            ▇ [9ms] - #43 Require(35) node_modules/egg-multipart/app/extend/context.js
                            ▇ [0ms] - #44 Require(36) node_modules/egg-view/app/extend/context.js
                            ▇ [0ms] - #45 Require(37) node_modules/egg/app/extend/context.js
                            ▇ [10ms] - #46 Load extend/helper.js
                            ▇ [7ms] - #47 Require(38) node_modules/egg-security/app/extend/helper.js
                             ▇ [0ms] - #48 Require(39) node_modules/egg/app/extend/helper.js
                             ▇ [16ms] - #49 Load app.js
                             ▇ [0ms] - #50 Require(40) node_modules/egg-session/app.js
                             ▇ [1ms] - #51 Require(41) node_modules/egg-security/app.js
                             ▇ [4ms] - #52 Require(42) node_modules/egg-onerror/app.js
                             ▇ [4ms] - #53 Require(43) node_modules/egg-i18n/app.js
                             ▇ [4ms] - #54 Require(44) node_modules/egg-watcher/app.js
                              ▇ [0ms] - #55 Require(45) node_modules/egg-schedule/app.js
                              ▇ [1ms] - #56 Require(46) node_modules/egg-multipart/app.js
                              ▇ [0ms] - #57 Require(47) node_modules/egg-development/app.js
                              ▇ [0ms] - #58 Require(48) node_modules/egg-logrotator/app.js
                              ▇ [1ms] - #59 Require(49) node_modules/egg-static/app.js
                              ▇ [0ms] - #60 Require(50) node_modules/egg-sequelize/app.js
                              ▇ [0ms] - #61 Require(51) node_modules/egg-cors/app.js
                              ▇ [1ms] - #62 Require(52) node_modules/egg-view-nunjucks/app.js
                              ▇ [0ms] - #63 Require(53) app.js
                              ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [289ms] - #64 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                           ▇ [11ms] - #65 Load "Symbol(model)" to Application
                                            ▇▇▇▇▇ [87ms] - #66 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
                                            ▇▇▇▇ [67ms] - #67 Did Load in app.js:didLoad
                                            ▇ [4ms] - #68 Load Service
                                            ▇ [3ms] - #69 Load "service" to Context
                                            ▇▇ [45ms] - #70 Load Middleware
                                            ▇▇ [40ms] - #71 Load "middlewares" to Application
                                               ▇ [4ms] - #72 Load Controller
                                               ▇ [4ms] - #73 Load "controller" to Application
                                               ▇ [2ms] - #74 Load Router
                                               ▇ [1ms] - #75 Require(54) app/router.js
                                               ▇ [12ms] - #76 Before Start in node_modules/egg-core/lib/egg.js:328:10
                                                 ▇ [1ms] - #77 Will Ready in app.js:willReady
2025-08-21 23:51:32,113 INFO 30536 [egg:core] dump config after ready, 3ms
2025-08-21 23:51:37,321 INFO 30744 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\logs\\plan-map-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"plan-map-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"application","localStorage":{"enabled":false}}
2025-08-21 23:51:37,322 INFO 30744 [egg-multipart] file mode enable
2025-08-21 23:51:37,322 INFO 30744 [egg-multipart] will save temporary files to "C:\\Users\\<USER>\\AppData\\Local\\Temp\\egg-multipart-tmp\\plan-map-node", cleanup job cron: "0 30 4 * * *"
2025-08-21 23:51:37,616 INFO 30744 [egg-static] starting static serve /uploads -> C:\Users\<USER>\Desktop\H5地图规划\后端\uploads
2025-08-21 23:51:37,618 INFO 30744 [egg-security] use methodnoallow middleware
2025-08-21 23:51:37,618 INFO 30744 [egg-security] use noopen middleware
2025-08-21 23:51:37,619 INFO 30744 [egg-security] use nosniff middleware
2025-08-21 23:51:37,620 INFO 30744 [egg-security] use xssProtection middleware
2025-08-21 23:51:37,620 INFO 30744 [egg-security] use xframe middleware
2025-08-21 23:51:37,620 INFO 30744 [egg-security] use dta middleware
2025-08-21 23:51:37,620 INFO 30744 [egg-security] compose 6 middlewares into one security middleware
2025-08-21 23:51:37,630 INFO 30744 [egg:core] dump config after load, 4ms
2025-08-21 23:51:37,644 INFO 30744 [egg-watcher:application] watcher start success
2025-08-21 23:51:37,664 INFO 30744 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [423ms] - #0 Process Start
                        ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [448ms] - #1 Application Start
                        ▇ [10ms] - #2 Load Plugin
                         ▇ [12ms] - #3 Load Config
                         ▇ [1ms] - #4 Require(0) config/config.default.js
                         ▇ [0ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                         ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                         ▇ [0ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                         ▇ [0ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                         ▇ [1ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                         ▇ [0ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                         ▇ [1ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                         ▇ [0ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                         ▇ [0ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                         ▇ [0ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                         ▇ [0ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                         ▇ [1ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                         ▇ [0ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                         ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                         ▇ [0ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                         ▇ [0ms] - #20 Require(16) node_modules/egg/config/config.default.js
                         ▇ [0ms] - #21 Require(17) config/config.default.js
                         ▇ [0ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                         ▇ [0ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                         ▇ [1ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                          ▇ [0ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                          ▇ [0ms] - #26 Require(22) node_modules/egg/config/config.local.js
                          ▇ [22ms] - #27 Load extend/application.js
                          ▇ [1ms] - #28 Require(23) node_modules/egg-session/app/extend/application.js
                          ▇ [0ms] - #29 Require(24) node_modules/egg-security/app/extend/application.js
                          ▇ [1ms] - #30 Require(25) node_modules/egg-jsonp/app/extend/application.js
                          ▇ [2ms] - #31 Require(26) node_modules/egg-schedule/app/extend/application.js
                          ▇ [0ms] - #32 Require(27) node_modules/egg-logrotator/app/extend/application.js
                          ▇ [1ms] - #33 Require(28) node_modules/egg-view/app/extend/application.js
                          ▇ [14ms] - #34 Require(29) node_modules/egg-view-nunjucks/app/extend/application.js
                           ▇ [3ms] - #35 Load extend/request.js
                           ▇ [1ms] - #36 Require(30) node_modules/egg/app/extend/request.js
                           ▇ [3ms] - #37 Load extend/response.js
                           ▇ [1ms] - #38 Require(31) node_modules/egg/app/extend/response.js
                           ▇ [19ms] - #39 Load extend/context.js
                           ▇ [5ms] - #40 Require(32) node_modules/egg-security/app/extend/context.js
                            ▇ [0ms] - #41 Require(33) node_modules/egg-jsonp/app/extend/context.js
                            ▇ [0ms] - #42 Require(34) node_modules/egg-i18n/app/extend/context.js
                            ▇ [9ms] - #43 Require(35) node_modules/egg-multipart/app/extend/context.js
                            ▇ [1ms] - #44 Require(36) node_modules/egg-view/app/extend/context.js
                            ▇ [1ms] - #45 Require(37) node_modules/egg/app/extend/context.js
                            ▇ [10ms] - #46 Load extend/helper.js
                            ▇ [8ms] - #47 Require(38) node_modules/egg-security/app/extend/helper.js
                             ▇ [0ms] - #48 Require(39) node_modules/egg/app/extend/helper.js
                             ▇ [17ms] - #49 Load app.js
                             ▇ [1ms] - #50 Require(40) node_modules/egg-session/app.js
                             ▇ [0ms] - #51 Require(41) node_modules/egg-security/app.js
                             ▇ [3ms] - #52 Require(42) node_modules/egg-onerror/app.js
                             ▇ [4ms] - #53 Require(43) node_modules/egg-i18n/app.js
                             ▇ [4ms] - #54 Require(44) node_modules/egg-watcher/app.js
                              ▇ [0ms] - #55 Require(45) node_modules/egg-schedule/app.js
                              ▇ [1ms] - #56 Require(46) node_modules/egg-multipart/app.js
                              ▇ [0ms] - #57 Require(47) node_modules/egg-development/app.js
                              ▇ [1ms] - #58 Require(48) node_modules/egg-logrotator/app.js
                              ▇ [0ms] - #59 Require(49) node_modules/egg-static/app.js
                              ▇ [0ms] - #60 Require(50) node_modules/egg-sequelize/app.js
                              ▇ [1ms] - #61 Require(51) node_modules/egg-cors/app.js
                              ▇ [0ms] - #62 Require(52) node_modules/egg-view-nunjucks/app.js
                              ▇ [0ms] - #63 Require(53) app.js
                              ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [320ms] - #64 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                           ▇ [11ms] - #65 Load "Symbol(model)" to Application
                                            ▇▇▇▇▇ [93ms] - #66 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
                                            ▇▇▇ [70ms] - #67 Did Load in app.js:didLoad
                                            ▇ [4ms] - #68 Load Service
                                            ▇ [4ms] - #69 Load "service" to Context
                                            ▇▇ [48ms] - #70 Load Middleware
                                            ▇▇ [43ms] - #71 Load "middlewares" to Application
                                               ▇ [4ms] - #72 Load Controller
                                               ▇ [4ms] - #73 Load "controller" to Application
                                               ▇ [2ms] - #74 Load Router
                                               ▇ [0ms] - #75 Require(54) app/router.js
                                               ▇ [13ms] - #76 Before Start in node_modules/egg-core/lib/egg.js:328:10
                                                 ▇ [0ms] - #77 Will Ready in app.js:willReady
2025-08-21 23:51:37,665 INFO 30744 [egg:core] dump config after ready, 4ms
2025-08-21 23:52:07,291 INFO 30224 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\logs\\plan-map-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"plan-map-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"application","localStorage":{"enabled":false}}
2025-08-21 23:52:07,291 INFO 30224 [egg-multipart] file mode enable
2025-08-21 23:52:07,291 INFO 30224 [egg-multipart] will save temporary files to "C:\\Users\\<USER>\\AppData\\Local\\Temp\\egg-multipart-tmp\\plan-map-node", cleanup job cron: "0 30 4 * * *"
2025-08-21 23:52:07,561 INFO 30224 [egg-static] starting static serve /uploads -> C:\Users\<USER>\Desktop\H5地图规划\后端\uploads
2025-08-21 23:52:07,562 INFO 30224 [egg-security] use methodnoallow middleware
2025-08-21 23:52:07,563 INFO 30224 [egg-security] use noopen middleware
2025-08-21 23:52:07,564 INFO 30224 [egg-security] use nosniff middleware
2025-08-21 23:52:07,564 INFO 30224 [egg-security] use xssProtection middleware
2025-08-21 23:52:07,565 INFO 30224 [egg-security] use xframe middleware
2025-08-21 23:52:07,565 INFO 30224 [egg-security] use dta middleware
2025-08-21 23:52:07,565 INFO 30224 [egg-security] compose 6 middlewares into one security middleware
2025-08-21 23:52:07,574 INFO 30224 [egg:core] dump config after load, 3ms
2025-08-21 23:52:07,594 INFO 30224 [egg-watcher:application] watcher start success
2025-08-21 23:52:07,605 INFO 30224 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [398ms] - #0 Process Start
                        ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [415ms] - #1 Application Start
                        ▇ [10ms] - #2 Load Plugin
                         ▇ [11ms] - #3 Load Config
                         ▇ [0ms] - #4 Require(0) config/config.default.js
                         ▇ [0ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                         ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                         ▇ [0ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                         ▇ [0ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                         ▇ [0ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                         ▇ [0ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                         ▇ [0ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                         ▇ [0ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                         ▇ [1ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                         ▇ [0ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                         ▇ [0ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                         ▇ [0ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                         ▇ [0ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                         ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                         ▇ [0ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                         ▇ [0ms] - #20 Require(16) node_modules/egg/config/config.default.js
                          ▇ [0ms] - #21 Require(17) config/config.default.js
                          ▇ [0ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                          ▇ [0ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                          ▇ [0ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                          ▇ [0ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                          ▇ [0ms] - #26 Require(22) node_modules/egg/config/config.local.js
                          ▇ [22ms] - #27 Load extend/application.js
                          ▇ [0ms] - #28 Require(23) node_modules/egg-session/app/extend/application.js
                          ▇ [0ms] - #29 Require(24) node_modules/egg-security/app/extend/application.js
                          ▇ [2ms] - #30 Require(25) node_modules/egg-jsonp/app/extend/application.js
                          ▇ [1ms] - #31 Require(26) node_modules/egg-schedule/app/extend/application.js
                          ▇ [0ms] - #32 Require(27) node_modules/egg-logrotator/app/extend/application.js
                          ▇ [0ms] - #33 Require(28) node_modules/egg-view/app/extend/application.js
                          ▇ [13ms] - #34 Require(29) node_modules/egg-view-nunjucks/app/extend/application.js
                           ▇ [3ms] - #35 Load extend/request.js
                           ▇ [1ms] - #36 Require(30) node_modules/egg/app/extend/request.js
                           ▇ [3ms] - #37 Load extend/response.js
                           ▇ [1ms] - #38 Require(31) node_modules/egg/app/extend/response.js
                           ▇ [17ms] - #39 Load extend/context.js
                           ▇ [5ms] - #40 Require(32) node_modules/egg-security/app/extend/context.js
                            ▇ [0ms] - #41 Require(33) node_modules/egg-jsonp/app/extend/context.js
                            ▇ [0ms] - #42 Require(34) node_modules/egg-i18n/app/extend/context.js
                            ▇ [8ms] - #43 Require(35) node_modules/egg-multipart/app/extend/context.js
                            ▇ [0ms] - #44 Require(36) node_modules/egg-view/app/extend/context.js
                            ▇ [1ms] - #45 Require(37) node_modules/egg/app/extend/context.js
                            ▇ [11ms] - #46 Load extend/helper.js
                             ▇ [7ms] - #47 Require(38) node_modules/egg-security/app/extend/helper.js
                             ▇ [0ms] - #48 Require(39) node_modules/egg/app/extend/helper.js
                             ▇ [17ms] - #49 Load app.js
                             ▇ [0ms] - #50 Require(40) node_modules/egg-session/app.js
                             ▇ [1ms] - #51 Require(41) node_modules/egg-security/app.js
                             ▇ [4ms] - #52 Require(42) node_modules/egg-onerror/app.js
                             ▇ [5ms] - #53 Require(43) node_modules/egg-i18n/app.js
                              ▇ [3ms] - #54 Require(44) node_modules/egg-watcher/app.js
                              ▇ [1ms] - #55 Require(45) node_modules/egg-schedule/app.js
                              ▇ [0ms] - #56 Require(46) node_modules/egg-multipart/app.js
                              ▇ [0ms] - #57 Require(47) node_modules/egg-development/app.js
                              ▇ [0ms] - #58 Require(48) node_modules/egg-logrotator/app.js
                              ▇ [0ms] - #59 Require(49) node_modules/egg-static/app.js
                              ▇ [1ms] - #60 Require(50) node_modules/egg-sequelize/app.js
                              ▇ [0ms] - #61 Require(51) node_modules/egg-cors/app.js
                              ▇ [0ms] - #62 Require(52) node_modules/egg-view-nunjucks/app.js
                              ▇ [1ms] - #63 Require(53) app.js
                              ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [300ms] - #64 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                           ▇ [10ms] - #65 Load "Symbol(model)" to Application
                                            ▇▇▇▇▇ [87ms] - #66 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
                                            ▇▇▇▇ [67ms] - #67 Did Load in app.js:didLoad
                                            ▇ [4ms] - #68 Load Service
                                            ▇ [4ms] - #69 Load "service" to Context
                                            ▇▇ [46ms] - #70 Load Middleware
                                            ▇▇ [41ms] - #71 Load "middlewares" to Application
                                               ▇ [4ms] - #72 Load Controller
                                               ▇ [4ms] - #73 Load "controller" to Application
                                               ▇ [2ms] - #74 Load Router
                                               ▇ [0ms] - #75 Require(54) app/router.js
                                               ▇ [13ms] - #76 Before Start in node_modules/egg-core/lib/egg.js:328:10
                                                 ▇ [0ms] - #77 Will Ready in app.js:willReady
2025-08-21 23:52:07,606 INFO 30224 [egg:core] dump config after ready, 4ms
2025-08-22 00:00:00,010 INFO 30224 [-/127.0.0.1/-/2ms SCHEDULE /__schedule?path=C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-logrotator\app\schedule\clean_log.js&type=worker&cron=0%200%20*%20*%20*] [egg-logrotator] clean all log before 31 days
2025-08-22 00:00:01,019 INFO 30224 [-/127.0.0.1/-/5ms SCHEDULE /__schedule?path=C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js&type=worker&cron=1%200%200%20*%20*%20*] [egg-logrotator] broadcast log-reload
2025-08-22 00:00:01,019 INFO 30224 [-/127.0.0.1/-/5ms SCHEDULE /__schedule?path=C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js&type=worker&cron=1%200%200%20*%20*%20*] [egg-logrotator] rotate files success by DayRotator, files ["C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\logs\\plan-map-node\\common-error.log -> C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\logs\\plan-map-node\\common-error.log.2025-08-21","C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\logs\\plan-map-node\\plan-map-node-web.log -> C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\logs\\plan-map-node\\plan-map-node-web.log.2025-08-21","C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\logs\\plan-map-node\\egg-web.log -> C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\logs\\plan-map-node\\egg-web.log.2025-08-21","C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\logs\\plan-map-node\\egg-schedule.log -> C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\logs\\plan-map-node\\egg-schedule.log.2025-08-21","C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\logs\\plan-map-node\\egg-agent.log -> C:\\Users\\<USER>\\Desktop\\H5地图规划\\后端\\logs\\plan-map-node\\egg-agent.log.2025-08-21"]

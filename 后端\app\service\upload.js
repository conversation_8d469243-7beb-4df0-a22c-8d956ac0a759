'use strict';

const Service = require('egg').Service;
const fs = require('fs');
const path = require('path');

class UploadService extends Service {
  /**
   * 保存文件信息到数据库
   */
  async saveFileInfo(fileInfo) {
    // 这里可以调用数据库模型保存文件信息
    // 暂时返回文件信息
    return fileInfo;
  }

  /**
   * 获取文件信息
   */
  async getFileInfo(fileName) {
    const uploadDir = path.join(this.app.baseDir, 'app/public/uploads');
    const filePath = path.join(uploadDir, fileName);
    
    if (!fs.existsSync(filePath)) {
      return null;
    }
    
    const stats = fs.statSync(filePath);
    return {
      fileName,
      filePath: `/uploads/${fileName}`,
      fileSize: stats.size,
      uploadTime: stats.mtime
    };
  }

  /**
   * 检查文件类型是否允许
   */
  isAllowedFileType(fileName) {
    const allowedTypes = [
      '.jpg', '.jpeg', '.png', '.gif', '.bmp', // 图片
      '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', // 文档
      '.txt', '.md', '.json', '.xml', // 文本
      '.zip', '.rar', '.7z', // 压缩包
      '.mp3', '.mp4', '.avi', '.mov' // 媒体文件
    ];
    
    const ext = path.extname(fileName).toLowerCase();
    return allowedTypes.includes(ext);
  }

  /**
   * 检查文件大小是否在限制内
   */
  isFileSizeAllowed(fileSize) {
    const maxSize = 10 * 1024 * 1024; // 10MB
    return fileSize <= maxSize;
  }

  /**
   * 清理过期文件
   */
  async cleanExpiredFiles(expireDays = 30) {
    const uploadDir = path.join(this.app.baseDir, 'app/public/uploads');
    
    if (!fs.existsSync(uploadDir)) {
      return;
    }
    
    const files = fs.readdirSync(uploadDir);
    const now = new Date();
    const expireTime = expireDays * 24 * 60 * 60 * 1000;
    
    for (const file of files) {
      const filePath = path.join(uploadDir, file);
      const stats = fs.statSync(filePath);
      
      if (stats.isFile() && (now - stats.mtime.getTime()) > expireTime) {
        try {
          fs.unlinkSync(filePath);
          this.ctx.logger.info(`清理过期文件: ${file}`);
        } catch (error) {
          this.ctx.logger.error(`清理文件失败: ${file}`, error);
        }
      }
    }
  }

  /**
   * 获取上传目录统计信息
   */
  async getUploadStats() {
    const uploadDir = path.join(this.app.baseDir, 'app/public/uploads');
    
    if (!fs.existsSync(uploadDir)) {
      return {
        totalFiles: 0,
        totalSize: 0,
        directory: uploadDir
      };
    }
    
    const files = fs.readdirSync(uploadDir);
    let totalSize = 0;
    let fileCount = 0;
    
    for (const file of files) {
      const filePath = path.join(uploadDir, file);
      const stats = fs.statSync(filePath);
      
      if (stats.isFile()) {
        totalSize += stats.size;
        fileCount++;
      }
    }
    
    return {
      totalFiles: fileCount,
      totalSize: totalSize,
      directory: uploadDir
    };
  }
}

module.exports = UploadService; 
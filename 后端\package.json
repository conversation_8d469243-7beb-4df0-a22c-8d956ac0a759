{"name": "plan-map-node", "version": "1.0.0", "description": "地图标点规划后端", "private": true, "egg": {"declarations": true}, "dependencies": {"axios": "^1.7.7", "egg": "^3.17.5", "egg-cors": "^3.0.1", "egg-multipart": "^3.5.0", "egg-schedule": "^4.0.1", "egg-scripts": "2", "egg-sequelize": "^6.0.0", "egg-view-nunjucks": "^2.3.0", "moment": "^2.30.1", "multer": "^2.0.2", "mysql2": "^3.11.3"}, "devDependencies": {"egg-bin": "6", "egg-mock": "5", "eslint": "8", "eslint-config-egg": "13"}, "engines": {"node": ">=18.0.0"}, "scripts": {"start": "egg-scripts start --daemon --title=egg-server-problem-node", "stop": "egg-scripts stop --title=egg-server-problem-node", "dev": "egg-bin dev", "test": "npm run lint -- --fix && npm run test:local", "test:local": "egg-bin test", "cov": "egg-bin cov", "lint": "eslint .", "ci": "npm run lint && npm run cov"}, "repository": {"type": "git", "url": ""}, "author": "yamlling", "license": "MIT"}
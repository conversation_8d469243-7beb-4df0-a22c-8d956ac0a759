[{"name": null, "methods": ["POST"], "paramNames": [], "path": "/citys/list", "regexp": "/^\\/citys\\/list(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/citys/save", "regexp": "/^\\/citys\\/save(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/citys/delete", "regexp": "/^\\/citys\\/delete(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/citys/setdefault", "regexp": "/^\\/citys\\/setdefault(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/location/list", "regexp": "/^\\/location\\/list(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/location/save", "regexp": "/^\\/location\\/save(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/location/delete", "regexp": "/^\\/location\\/delete(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/user/list", "regexp": "/^\\/user\\/list(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/user/save", "regexp": "/^\\/user\\/save(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/user/delete", "regexp": "/^\\/user\\/delete(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/user/login", "regexp": "/^\\/user\\/login(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/user/register", "regexp": "/^\\/user\\/register(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/paths/list", "regexp": "/^\\/paths\\/list(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/paths/save", "regexp": "/^\\/paths\\/save(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/paths/delete", "regexp": "/^\\/paths\\/delete(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/foods/list", "regexp": "/^\\/foods\\/list(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/foods/save", "regexp": "/^\\/foods\\/save(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/foods/delete", "regexp": "/^\\/foods\\/delete(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/hotels/list", "regexp": "/^\\/hotels\\/list(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/hotels/save", "regexp": "/^\\/hotels\\/save(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/hotels/delete", "regexp": "/^\\/hotels\\/delete(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/upload/file", "regexp": "/^\\/upload\\/file(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/upload/list", "regexp": "/^\\/upload\\/list(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/upload/delete", "regexp": "/^\\/upload\\/delete(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "fileName", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/upload/download/:fileName", "regexp": "/^\\/upload\\/download\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/upload/stats", "regexp": "/^\\/upload\\/stats(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/", "regexp": "/^(?:\\/(?=$))?$/", "stack": ["wrappedController"]}]
import matplotlib
import matplotlib.pyplot as plt
import torch
import torch.nn as nn
import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler
from torch.utils.data import Dataset, DataLoader
from sklearn.metrics import r2_score
import pickle
matplotlib.use('Agg')  # 设置非交互式后端


# 数据预处理类
class ChlDataPreprocessor:
    def __init__(self, data_path):
        self.data_path = data_path
        self.feature_scaler = MinMaxScaler()
        self.target_scaler = MinMaxScaler()

    def clean_numeric_data(self, df, columns):
        """清理数值数据，将无效值替换为NaN"""
        for col in columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        return df

    def load_and_preprocess(self):
        # 读取数据
        df = pd.read_csv(self.data_path, low_memory=False)

        # 提取特征
        features = ['lon', 'lat', 'date', 'SST']
        # 清理特征数据
        df = self.clean_numeric_data(df, features)

        # 添加特征工程
        df['date'] = pd.to_datetime(df['date'])
        df['month'] = df['date'].dt.month
        df['day_of_year'] = df['date'].dt.dayofyear

        # 使用新的特征集
        features = ['lon', 'lat', 'SST', 'month', 'day_of_year']
        X = df[features].values.astype(np.float32)

        # 只提取300米深度的叶绿素数据
        chl_column = 'file.300'
        df = self.clean_numeric_data(df, [chl_column])
        y = df[chl_column].values.astype(np.float32).reshape(-1, 1)

        # 数据清洗
        mask = ~np.isnan(y)
        valid_indices = np.all(mask, axis=1)
        X = X[valid_indices]
        y = y[valid_indices]

        # 移除异常值（使用IQR方法）
        Q1 = np.percentile(y, 25)
        Q3 = np.percentile(y, 75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        mask = (y >= lower_bound) & (y <= upper_bound)
        valid_indices = np.all(mask, axis=1)
        X = X[valid_indices]
        y = y[valid_indices]

        # 数据归一化
        X = self.feature_scaler.fit_transform(X)
        y = self.target_scaler.fit_transform(y)

        return X, y

    def transform_features(self, X):
        """转换特征数据"""
        return self.feature_scaler.transform(X)

    def inverse_transform_target(self, y):
        """反转换目标数据"""
        return self.target_scaler.inverse_transform(y)


# 自定义数据集类
class ChlDataset(Dataset):
    def __init__(self, X, y):
        self.X = torch.FloatTensor(X)
        self.y = torch.FloatTensor(y)

    def __len__(self):
        return len(self.X)

    def __getitem__(self, idx):
        return self.X[idx], self.y[idx]


# LSTM-CNN模型定义
class ChlPredictionModel(nn.Module):
    def __init__(self, input_size, hidden_size, num_layers):
        super(ChlPredictionModel, self).__init__()

        # LSTM层
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            batch_first=True,
            dropout=0.3  # 添加dropout
        )

        # 简化CNN层
        self.conv1 = nn.Conv1d(hidden_size, 32, kernel_size=3, padding=1)

        # 全连接层
        self.fc1 = nn.Linear(32, 64)
        self.fc2 = nn.Linear(64, 1)

        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.3)
        self.batch_norm1 = nn.BatchNorm1d(32)
        self.batch_norm2 = nn.BatchNorm1d(64)

    def forward(self, x):
        x = x.unsqueeze(1)

        # LSTM处理
        lstm_out, _ = self.lstm(x)

        # CNN处理
        conv_out = self.relu(self.conv1(lstm_out.transpose(1, 2)))
        conv_out = self.batch_norm1(conv_out)

        # 展平
        flat_out = conv_out.squeeze(-1)

        # 全连接层
        out = self.relu(self.fc1(flat_out))
        out = self.batch_norm2(out)
        out = self.dropout(out)
        out = self.fc2(out)

        return out


# 训练函数
def train_model(model, train_loader, criterion, optimizer, device):
    model.train()
    total_loss = 0

    for batch_X, batch_y in train_loader:
        batch_X, batch_y = batch_X.to(device), batch_y.to(device)

        optimizer.zero_grad()
        outputs = model(batch_X)
        loss = criterion(outputs, batch_y)
        loss.backward()
        optimizer.step()

        total_loss += loss.item()

    return total_loss / len(train_loader)


# 评估函数
def evaluate_model(model, test_loader, criterion, device):
    model.eval()
    total_loss = 0
    predictions = []
    actuals = []

    with torch.no_grad():
        for batch_X, batch_y in test_loader:
            batch_X, batch_y = batch_X.to(device), batch_y.to(device)
            outputs = model(batch_X)
            loss = criterion(outputs, batch_y)
            total_loss += loss.item()

            predictions.extend(outputs.cpu().numpy())
            actuals.extend(batch_y.cpu().numpy())

    return (total_loss / len(test_loader),
            np.array(predictions),
            np.array(actuals))


def main():
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)

    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f'使用设备: {device}')

    # 数据预处理
    preprocessor = ChlDataPreprocessor('西北太平洋.csv')
    X, y = preprocessor.load_and_preprocess()
    print(f'数据形状: X={X.shape}, y={y.shape}')

    # 保存数据预处理器
    with open('preprocessor.pkl', 'wb') as f:
        pickle.dump(preprocessor, f)

    # 划分训练集和测试集
    train_size = int(0.8 * len(X))
    X_train, X_test = X[:train_size], X[train_size:]
    y_train, y_test = y[:train_size], y[train_size:]

    # 创建数据加载器
    train_dataset = ChlDataset(X_train, y_train)
    test_dataset = ChlDataset(X_test, y_test)

    train_loader = DataLoader(train_dataset, batch_size=64, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=64, shuffle=False)

    # 初始化模型
    input_size = 5  # lon, lat, SST, month, day_of_year
    hidden_size = 64
    num_layers = 2

    model = ChlPredictionModel(input_size, hidden_size, num_layers)
    model = model.to(device)

    # 定义损失函数和优化器
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.5, patience=5, verbose=True)

    # 训练模型
    num_epochs = 100
    train_losses = []
    test_losses = []
    best_test_loss = float('inf')
    patience = 10
    patience_counter = 0

    for epoch in range(num_epochs):
        train_loss = train_model(
            model, train_loader, criterion, optimizer, device)
        test_loss, predictions, actuals = evaluate_model(
            model, test_loader, criterion, device)

        # 更新学习率
        scheduler.step(test_loss)

        train_losses.append(train_loss)
        test_losses.append(test_loss)

        print(
            f'Epoch [{epoch+1}/{num_epochs}], Train Loss: {train_loss:.4f}, Test Loss: {test_loss:.4f}')

        # 早停
        if test_loss < best_test_loss:
            best_test_loss = test_loss
            patience_counter = 0
            # 保存最佳模型
            torch.save({
                'model_state_dict': model.state_dict(),
                'input_size': input_size,
                'hidden_size': hidden_size,
                'num_layers': num_layers
            }, 'best_chl_model.pth')
        else:
            patience_counter += 1
            if patience_counter >= patience:
                print(f'Early stopping at epoch {epoch+1}')
                break

    # 绘制损失曲线
    plt.figure(figsize=(10, 5))
    plt.plot(train_losses, label='train_losses')
    plt.plot(test_losses, label='test_losses')
    plt.xlabel('train_epochs')
    plt.ylabel('loss')
    plt.title('train_losses and test_losses')
    plt.legend()
    plt.savefig('loss_curve.png')
    plt.close()

    # 计算评估指标
    mse = np.mean((predictions - actuals) ** 2)
    rmse = np.sqrt(mse)
    mae = np.mean(np.abs(predictions - actuals))
    r2 = r2_score(actuals, predictions)

    print('\n模型评估指标:')
    print(f'MSE: {mse:.4f}')
    print(f'RMSE: {rmse:.4f}')
    print(f'MAE: {mae:.4f}')
    print(f'R2: {r2:.4f}')

    # 返回评估指标
    return {
        'mse': float(mse),
        'rmse': float(rmse),
        'mae': float(mae),
        'r2': float(r2)
    }


if __name__ == '__main__':
    main()

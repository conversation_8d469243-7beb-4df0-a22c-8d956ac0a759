// This file is created by egg-ts-helper@2.1.1
// Do not modify this file!!!!!!!!!
/* eslint-disable */

import 'egg';
import ExportCitys = require('../../../app/model/citys');
import ExportFoods = require('../../../app/model/foods');
import ExportHotels = require('../../../app/model/hotels');
import ExportLocation = require('../../../app/model/location');
import ExportPaths = require('../../../app/model/paths');
import ExportUpload = require('../../../app/model/upload');
import ExportUser = require('../../../app/model/user');

declare module 'egg' {
  interface IModel {
    Citys: ReturnType<typeof ExportCitys>;
    Foods: ReturnType<typeof ExportFoods>;
    Hotels: ReturnType<typeof ExportHotels>;
    Location: ReturnType<typeof ExportLocation>;
    Paths: ReturnType<typeof ExportPaths>;
    Upload: ReturnType<typeof ExportUpload>;
    User: ReturnType<typeof ExportUser>;
  }
}

2025-08-04 19:59:16,434 ERROR 20076 [-/127.0.0.1/-/7ms GET /] nodejs.AssertionError: Can't find index.html from 
    at ViewManager.resolve (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\view_manager.js:74:5)
    at async [contextView#render] (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\context_view.js:52:22)
    at async Object.<anonymous> (C:\Users\<USER>\Desktop\H5地图规划\后端\app\router.js:33:5)
generatedMessage: false
code: "ERR_ASSERTION"
actual: undefined
expected: true
operator: "=="
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 20076
hostname: l

2025-08-04 19:59:42,261 ERROR 12312 [-/127.0.0.1/-/5ms GET /] nodejs.AssertionError: Can't find index.html from 
    at ViewManager.resolve (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\view_manager.js:74:5)
    at async [contextView#render] (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\context_view.js:52:22)
    at async Object.<anonymous> (C:\Users\<USER>\Desktop\H5地图规划\后端\app\router.js:33:5)
generatedMessage: false
code: "ERR_ASSERTION"
actual: undefined
expected: true
operator: "=="
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 12312
hostname: l

2025-08-04 19:59:43,023 ERROR 12312 [-/127.0.0.1/-/1ms GET /] nodejs.AssertionError: Can't find index.html from 
    at ViewManager.resolve (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\view_manager.js:74:5)
    at async [contextView#render] (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\context_view.js:52:22)
    at async Object.<anonymous> (C:\Users\<USER>\Desktop\H5地图规划\后端\app\router.js:33:5)
generatedMessage: false
code: "ERR_ASSERTION"
actual: undefined
expected: true
operator: "=="
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 12312
hostname: l

2025-08-04 19:59:48,951 ERROR 12312 [-/127.0.0.1/-/1ms GET /] nodejs.AssertionError: Can't find index.html from 
    at ViewManager.resolve (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\view_manager.js:74:5)
    at async [contextView#render] (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\context_view.js:52:22)
    at async Object.<anonymous> (C:\Users\<USER>\Desktop\H5地图规划\后端\app\router.js:33:5)
generatedMessage: false
code: "ERR_ASSERTION"
actual: undefined
expected: true
operator: "=="
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 12312
hostname: l

2025-08-04 20:00:54,591 ERROR 20220 [-/127.0.0.1/-/4ms GET /] nodejs.AssertionError: Can't find index.html from 
    at ViewManager.resolve (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\view_manager.js:74:5)
    at async [contextView#render] (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\context_view.js:52:22)
    at async Object.<anonymous> (C:\Users\<USER>\Desktop\H5地图规划\后端\app\router.js:33:5)
generatedMessage: false
code: "ERR_ASSERTION"
actual: undefined
expected: true
operator: "=="
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 20220
hostname: l

2025-08-04 20:00:54,761 ERROR 20220 [-/127.0.0.1/-/1ms GET /] nodejs.AssertionError: Can't find index.html from 
    at ViewManager.resolve (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\view_manager.js:74:5)
    at async [contextView#render] (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\context_view.js:52:22)
    at async Object.<anonymous> (C:\Users\<USER>\Desktop\H5地图规划\后端\app\router.js:33:5)
generatedMessage: false
code: "ERR_ASSERTION"
actual: undefined
expected: true
operator: "=="
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 20220
hostname: l

2025-08-04 20:00:54,910 ERROR 20220 [-/127.0.0.1/-/1ms GET /] nodejs.AssertionError: Can't find index.html from 
    at ViewManager.resolve (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\view_manager.js:74:5)
    at async [contextView#render] (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\context_view.js:52:22)
    at async Object.<anonymous> (C:\Users\<USER>\Desktop\H5地图规划\后端\app\router.js:33:5)
generatedMessage: false
code: "ERR_ASSERTION"
actual: undefined
expected: true
operator: "=="
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 20220
hostname: l

2025-08-04 20:01:03,653 ERROR 22328 [-/127.0.0.1/-/5ms GET /] nodejs.AssertionError: Can't find index.html from 
    at ViewManager.resolve (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\view_manager.js:74:5)
    at async [contextView#render] (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\context_view.js:52:22)
    at async Object.<anonymous> (C:\Users\<USER>\Desktop\H5地图规划\后端\app\router.js:33:5)
generatedMessage: false
code: "ERR_ASSERTION"
actual: undefined
expected: true
operator: "=="
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 22328
hostname: l

2025-08-04 20:01:04,434 ERROR 22328 [-/127.0.0.1/-/1ms GET /] nodejs.AssertionError: Can't find index.html from 
    at ViewManager.resolve (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\view_manager.js:74:5)
    at async [contextView#render] (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\context_view.js:52:22)
    at async Object.<anonymous> (C:\Users\<USER>\Desktop\H5地图规划\后端\app\router.js:33:5)
generatedMessage: false
code: "ERR_ASSERTION"
actual: undefined
expected: true
operator: "=="
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 22328
hostname: l

2025-08-04 20:01:04,619 ERROR 22328 [-/127.0.0.1/-/1ms GET /] nodejs.AssertionError: Can't find index.html from 
    at ViewManager.resolve (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\view_manager.js:74:5)
    at async [contextView#render] (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-view\lib\context_view.js:52:22)
    at async Object.<anonymous> (C:\Users\<USER>\Desktop\H5地图规划\后端\app\router.js:33:5)
generatedMessage: false
code: "ERR_ASSERTION"
actual: undefined
expected: true
operator: "=="
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 22328
hostname: l

2025-08-21 23:56:13,711 ERROR 30224 [-/127.0.0.1/-/8ms POST /foods/list] nodejs.SequelizeDatabaseError: Unknown column 'foods.code' in 'where clause'
    at Query.run (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\dialects\mysql\query.js:52:25)
    at C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async MySQLQueryInterface.select (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\dialects\abstract\query-interface.js:407:12)
    at async foods.findAll (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1140:21)
    at async Promise.all (index 1)
    at async foods.findAndCountAll (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1322:27)
    at async Location.paginate (C:\Users\<USER>\Desktop\H5地图规划\后端\app\model\foods.js:31:20)
    at async FoodsController.list (C:\Users\<USER>\Desktop\H5地图规划\后端\app\controller\foods.js:74:13)
    at async C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-development\app\middleware\egg_loader_trace.js:7:50
name: "SequelizeDatabaseError"
parent: {"code":"ER_BAD_FIELD_ERROR","errno":1054,"sqlState":"42S22","sqlMessage":"Unknown column 'foods.code' in 'where clause'","sql":"SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO' ORDER BY `foods`.`id` DESC LIMIT 0, 999;"}
original: {"code":"ER_BAD_FIELD_ERROR","errno":1054,"sqlState":"42S22","sqlMessage":"Unknown column 'foods.code' in 'where clause'","sql":"SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO' ORDER BY `foods`.`id` DESC LIMIT 0, 999;"}
sql: "SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO' ORDER BY `foods`.`id` DESC LIMIT 0, 999;"
parameters: {}
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 30224
hostname: l

2025-08-21 23:56:26,156 ERROR 30224 [-/127.0.0.1/-/6ms POST /foods/list] nodejs.SequelizeDatabaseError: Unknown column 'foods.code' in 'where clause'
    at Query.run (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\dialects\mysql\query.js:52:25)
    at C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async MySQLQueryInterface.rawSelect (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\dialects\abstract\query-interface.js:434:18)
    at async foods.aggregate (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1277:19)
    at async foods.count (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1306:20)
    at async Promise.all (index 0)
    at async foods.findAndCountAll (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1322:27)
    at async Location.paginate (C:\Users\<USER>\Desktop\H5地图规划\后端\app\model\foods.js:31:20)
    at async FoodsController.list (C:\Users\<USER>\Desktop\H5地图规划\后端\app\controller\foods.js:74:13)
name: "SequelizeDatabaseError"
parent: {"code":"ER_BAD_FIELD_ERROR","errno":1054,"sqlState":"42S22","sqlMessage":"Unknown column 'foods.code' in 'where clause'","sql":"SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO';"}
original: {"code":"ER_BAD_FIELD_ERROR","errno":1054,"sqlState":"42S22","sqlMessage":"Unknown column 'foods.code' in 'where clause'","sql":"SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO';"}
sql: "SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO';"
parameters: {}
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 30224
hostname: l

2025-08-21 23:56:28,677 ERROR 30224 [-/127.0.0.1/-/2ms POST /foods/list] nodejs.SequelizeDatabaseError: Unknown column 'foods.code' in 'where clause'
    at Query.run (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\dialects\mysql\query.js:52:25)
    at C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async MySQLQueryInterface.rawSelect (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\dialects\abstract\query-interface.js:434:18)
    at async foods.aggregate (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1277:19)
    at async foods.count (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1306:20)
    at async Promise.all (index 0)
    at async foods.findAndCountAll (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1322:27)
    at async Location.paginate (C:\Users\<USER>\Desktop\H5地图规划\后端\app\model\foods.js:31:20)
    at async FoodsController.list (C:\Users\<USER>\Desktop\H5地图规划\后端\app\controller\foods.js:74:13)
name: "SequelizeDatabaseError"
parent: {"code":"ER_BAD_FIELD_ERROR","errno":1054,"sqlState":"42S22","sqlMessage":"Unknown column 'foods.code' in 'where clause'","sql":"SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO';"}
original: {"code":"ER_BAD_FIELD_ERROR","errno":1054,"sqlState":"42S22","sqlMessage":"Unknown column 'foods.code' in 'where clause'","sql":"SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO';"}
sql: "SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO';"
parameters: {}
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 30224
hostname: l

2025-08-21 23:56:32,850 ERROR 30224 [-/127.0.0.1/-/3ms POST /foods/list] nodejs.SequelizeDatabaseError: Unknown column 'foods.code' in 'where clause'
    at Query.run (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\dialects\mysql\query.js:52:25)
    at C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async MySQLQueryInterface.rawSelect (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\dialects\abstract\query-interface.js:434:18)
    at async foods.aggregate (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1277:19)
    at async foods.count (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1306:20)
    at async Promise.all (index 0)
    at async foods.findAndCountAll (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1322:27)
    at async Location.paginate (C:\Users\<USER>\Desktop\H5地图规划\后端\app\model\foods.js:31:20)
    at async FoodsController.list (C:\Users\<USER>\Desktop\H5地图规划\后端\app\controller\foods.js:74:13)
name: "SequelizeDatabaseError"
parent: {"code":"ER_BAD_FIELD_ERROR","errno":1054,"sqlState":"42S22","sqlMessage":"Unknown column 'foods.code' in 'where clause'","sql":"SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO';"}
original: {"code":"ER_BAD_FIELD_ERROR","errno":1054,"sqlState":"42S22","sqlMessage":"Unknown column 'foods.code' in 'where clause'","sql":"SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO';"}
sql: "SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO';"
parameters: {}
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 30224
hostname: l

2025-08-21 23:56:58,847 ERROR 30224 [-/127.0.0.1/-/4ms POST /foods/list] nodejs.SequelizeDatabaseError: Unknown column 'foods.code' in 'where clause'
    at Query.run (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\dialects\mysql\query.js:52:25)
    at C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async MySQLQueryInterface.select (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\dialects\abstract\query-interface.js:407:12)
    at async foods.findAll (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1140:21)
    at async Promise.all (index 1)
    at async foods.findAndCountAll (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1322:27)
    at async Location.paginate (C:\Users\<USER>\Desktop\H5地图规划\后端\app\model\foods.js:31:20)
    at async FoodsController.list (C:\Users\<USER>\Desktop\H5地图规划\后端\app\controller\foods.js:74:13)
    at async C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\egg-development\app\middleware\egg_loader_trace.js:7:50
name: "SequelizeDatabaseError"
parent: {"code":"ER_BAD_FIELD_ERROR","errno":1054,"sqlState":"42S22","sqlMessage":"Unknown column 'foods.code' in 'where clause'","sql":"SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO' ORDER BY `foods`.`id` DESC LIMIT 0, 999;"}
original: {"code":"ER_BAD_FIELD_ERROR","errno":1054,"sqlState":"42S22","sqlMessage":"Unknown column 'foods.code' in 'where clause'","sql":"SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO' ORDER BY `foods`.`id` DESC LIMIT 0, 999;"}
sql: "SELECT `id`, `title`, `lid`, `pid`, `img`, `time` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO' ORDER BY `foods`.`id` DESC LIMIT 0, 999;"
parameters: {}
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 30224
hostname: l

2025-08-21 23:57:16,727 ERROR 30224 [-/127.0.0.1/-/5ms POST /foods/list] nodejs.SequelizeDatabaseError: Unknown column 'foods.code' in 'where clause'
    at Query.run (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\dialects\mysql\query.js:52:25)
    at C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async MySQLQueryInterface.rawSelect (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\dialects\abstract\query-interface.js:434:18)
    at async foods.aggregate (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1277:19)
    at async foods.count (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1306:20)
    at async Promise.all (index 0)
    at async foods.findAndCountAll (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1322:27)
    at async Location.paginate (C:\Users\<USER>\Desktop\H5地图规划\后端\app\model\foods.js:31:20)
    at async FoodsController.list (C:\Users\<USER>\Desktop\H5地图规划\后端\app\controller\foods.js:74:13)
name: "SequelizeDatabaseError"
parent: {"code":"ER_BAD_FIELD_ERROR","errno":1054,"sqlState":"42S22","sqlMessage":"Unknown column 'foods.code' in 'where clause'","sql":"SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO';"}
original: {"code":"ER_BAD_FIELD_ERROR","errno":1054,"sqlState":"42S22","sqlMessage":"Unknown column 'foods.code' in 'where clause'","sql":"SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO';"}
sql: "SELECT count(*) AS `count` FROM `foods` AS `foods` WHERE `foods`.`code` = 'PATH_MDSM0MKR_0PEGO';"
parameters: {}
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 30224
hostname: l

2025-08-21 23:57:23,355 ERROR 30224 [-/127.0.0.1/-/4ms POST /hotels/list] nodejs.SequelizeDatabaseError: Unknown column 'hotels.code' in 'where clause'
    at Query.run (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\dialects\mysql\query.js:52:25)
    at C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async MySQLQueryInterface.rawSelect (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\dialects\abstract\query-interface.js:434:18)
    at async hotels.aggregate (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1277:19)
    at async hotels.count (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1306:20)
    at async Promise.all (index 0)
    at async hotels.findAndCountAll (C:\Users\<USER>\Desktop\H5地图规划\后端\node_modules\sequelize\lib\model.js:1322:27)
    at async Location.paginate (C:\Users\<USER>\Desktop\H5地图规划\后端\app\model\hotels.js:31:20)
    at async HotelsController.list (C:\Users\<USER>\Desktop\H5地图规划\后端\app\controller\hotels.js:74:13)
name: "SequelizeDatabaseError"
parent: {"code":"ER_BAD_FIELD_ERROR","errno":1054,"sqlState":"42S22","sqlMessage":"Unknown column 'hotels.code' in 'where clause'","sql":"SELECT count(*) AS `count` FROM `hotels` AS `hotels` WHERE `hotels`.`code` = 'PATH_MDSM0MKR_0PEGO';"}
original: {"code":"ER_BAD_FIELD_ERROR","errno":1054,"sqlState":"42S22","sqlMessage":"Unknown column 'hotels.code' in 'where clause'","sql":"SELECT count(*) AS `count` FROM `hotels` AS `hotels` WHERE `hotels`.`code` = 'PATH_MDSM0MKR_0PEGO';"}
sql: "SELECT count(*) AS `count` FROM `hotels` AS `hotels` WHERE `hotels`.`code` = 'PATH_MDSM0MKR_0PEGO';"
parameters: {}
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 30224
hostname: l


[{"name": "Process Start", "start": 1755792610484, "end": 1755792610875, "duration": 391, "pid": 18872, "index": 0}, {"name": "Application Start", "start": 1755792610876, "end": 1755792611293, "duration": 417, "pid": 18872, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1755792610883, "end": 1755792610894, "duration": 11, "pid": 18872, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1755792610894, "end": 1755792610906, "duration": 12, "pid": 18872, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1755792610895, "end": 1755792610895, "duration": 0, "pid": 18872, "index": 4}, {"name": "Require(1) node_modules/egg-session/config/config.default.js", "start": 1755792610896, "end": 1755792610896, "duration": 0, "pid": 18872, "index": 5}, {"name": "Require(2) node_modules/egg-security/config/config.default.js", "start": 1755792610896, "end": 1755792610896, "duration": 0, "pid": 18872, "index": 6}, {"name": "Require(3) node_modules/egg-jsonp/config/config.default.js", "start": 1755792610897, "end": 1755792610897, "duration": 0, "pid": 18872, "index": 7}, {"name": "Require(4) node_modules/egg-onerror/config/config.default.js", "start": 1755792610897, "end": 1755792610897, "duration": 0, "pid": 18872, "index": 8}, {"name": "Require(5) node_modules/egg-i18n/config/config.default.js", "start": 1755792610898, "end": 1755792610898, "duration": 0, "pid": 18872, "index": 9}, {"name": "Require(6) node_modules/egg-watcher/config/config.default.js", "start": 1755792610898, "end": 1755792610898, "duration": 0, "pid": 18872, "index": 10}, {"name": "Require(7) node_modules/egg-schedule/config/config.default.js", "start": 1755792610898, "end": 1755792610899, "duration": 1, "pid": 18872, "index": 11}, {"name": "Require(8) node_modules/egg-multipart/config/config.default.js", "start": 1755792610899, "end": 1755792610899, "duration": 0, "pid": 18872, "index": 12}, {"name": "Require(9) node_modules/egg-development/config/config.default.js", "start": 1755792610899, "end": 1755792610900, "duration": 1, "pid": 18872, "index": 13}, {"name": "Require(10) node_modules/egg-logrotator/config/config.default.js", "start": 1755792610900, "end": 1755792610900, "duration": 0, "pid": 18872, "index": 14}, {"name": "Require(11) node_modules/egg-static/config/config.default.js", "start": 1755792610900, "end": 1755792610900, "duration": 0, "pid": 18872, "index": 15}, {"name": "Require(12) node_modules/egg-view/config/config.default.js", "start": 1755792610901, "end": 1755792610901, "duration": 0, "pid": 18872, "index": 16}, {"name": "Require(13) node_modules/egg-sequelize/config/config.default.js", "start": 1755792610901, "end": 1755792610901, "duration": 0, "pid": 18872, "index": 17}, {"name": "Require(14) node_modules/egg-cors/config/config.default.js", "start": 1755792610901, "end": 1755792610901, "duration": 0, "pid": 18872, "index": 18}, {"name": "Require(15) node_modules/egg-view-nunjucks/config/config.default.js", "start": 1755792610902, "end": 1755792610902, "duration": 0, "pid": 18872, "index": 19}, {"name": "Require(16) node_modules/egg/config/config.default.js", "start": 1755792610902, "end": 1755792610902, "duration": 0, "pid": 18872, "index": 20}, {"name": "Require(17) config/config.default.js", "start": 1755792610903, "end": 1755792610903, "duration": 0, "pid": 18872, "index": 21}, {"name": "Require(18) node_modules/egg-security/config/config.local.js", "start": 1755792610903, "end": 1755792610903, "duration": 0, "pid": 18872, "index": 22}, {"name": "Require(19) node_modules/egg-watcher/config/config.local.js", "start": 1755792610904, "end": 1755792610904, "duration": 0, "pid": 18872, "index": 23}, {"name": "Require(20) node_modules/egg-view/config/config.local.js", "start": 1755792610905, "end": 1755792610905, "duration": 0, "pid": 18872, "index": 24}, {"name": "Require(21) node_modules/egg-view-nunjucks/config/config.local.js", "start": 1755792610906, "end": 1755792610906, "duration": 0, "pid": 18872, "index": 25}, {"name": "Require(22) node_modules/egg/config/config.local.js", "start": 1755792610906, "end": 1755792610906, "duration": 0, "pid": 18872, "index": 26}, {"name": "Load extend/application.js", "start": 1755792610907, "end": 1755792610929, "duration": 22, "pid": 18872, "index": 27}, {"name": "Require(23) node_modules/egg-session/app/extend/application.js", "start": 1755792610907, "end": 1755792610908, "duration": 1, "pid": 18872, "index": 28}, {"name": "Require(24) node_modules/egg-security/app/extend/application.js", "start": 1755792610908, "end": 1755792610908, "duration": 0, "pid": 18872, "index": 29}, {"name": "Require(25) node_modules/egg-jsonp/app/extend/application.js", "start": 1755792610909, "end": 1755792610910, "duration": 1, "pid": 18872, "index": 30}, {"name": "Require(26) node_modules/egg-schedule/app/extend/application.js", "start": 1755792610910, "end": 1755792610912, "duration": 2, "pid": 18872, "index": 31}, {"name": "Require(27) node_modules/egg-logrotator/app/extend/application.js", "start": 1755792610913, "end": 1755792610913, "duration": 0, "pid": 18872, "index": 32}, {"name": "Require(28) node_modules/egg-view/app/extend/application.js", "start": 1755792610913, "end": 1755792610914, "duration": 1, "pid": 18872, "index": 33}, {"name": "Require(29) node_modules/egg-view-nunjucks/app/extend/application.js", "start": 1755792610914, "end": 1755792610928, "duration": 14, "pid": 18872, "index": 34}, {"name": "Load extend/request.js", "start": 1755792610929, "end": 1755792610932, "duration": 3, "pid": 18872, "index": 35}, {"name": "Require(30) node_modules/egg/app/extend/request.js", "start": 1755792610930, "end": 1755792610931, "duration": 1, "pid": 18872, "index": 36}, {"name": "Load extend/response.js", "start": 1755792610932, "end": 1755792610935, "duration": 3, "pid": 18872, "index": 37}, {"name": "Require(31) node_modules/egg/app/extend/response.js", "start": 1755792610933, "end": 1755792610934, "duration": 1, "pid": 18872, "index": 38}, {"name": "Load extend/context.js", "start": 1755792610935, "end": 1755792610954, "duration": 19, "pid": 18872, "index": 39}, {"name": "Require(32) node_modules/egg-security/app/extend/context.js", "start": 1755792610935, "end": 1755792610940, "duration": 5, "pid": 18872, "index": 40}, {"name": "Require(33) node_modules/egg-jsonp/app/extend/context.js", "start": 1755792610941, "end": 1755792610941, "duration": 0, "pid": 18872, "index": 41}, {"name": "Require(34) node_modules/egg-i18n/app/extend/context.js", "start": 1755792610941, "end": 1755792610942, "duration": 1, "pid": 18872, "index": 42}, {"name": "Require(35) node_modules/egg-multipart/app/extend/context.js", "start": 1755792610942, "end": 1755792610951, "duration": 9, "pid": 18872, "index": 43}, {"name": "Require(36) node_modules/egg-view/app/extend/context.js", "start": 1755792610951, "end": 1755792610951, "duration": 0, "pid": 18872, "index": 44}, {"name": "Require(37) node_modules/egg/app/extend/context.js", "start": 1755792610952, "end": 1755792610953, "duration": 1, "pid": 18872, "index": 45}, {"name": "Load extend/helper.js", "start": 1755792610954, "end": 1755792610964, "duration": 10, "pid": 18872, "index": 46}, {"name": "Require(38) node_modules/egg-security/app/extend/helper.js", "start": 1755792610954, "end": 1755792610962, "duration": 8, "pid": 18872, "index": 47}, {"name": "Require(39) node_modules/egg/app/extend/helper.js", "start": 1755792610963, "end": 1755792610963, "duration": 0, "pid": 18872, "index": 48}, {"name": "Load app.js", "start": 1755792610964, "end": 1755792610982, "duration": 18, "pid": 18872, "index": 49}, {"name": "Require(40) node_modules/egg-session/app.js", "start": 1755792610964, "end": 1755792610964, "duration": 0, "pid": 18872, "index": 50}, {"name": "Require(41) node_modules/egg-security/app.js", "start": 1755792610965, "end": 1755792610965, "duration": 0, "pid": 18872, "index": 51}, {"name": "Require(42) node_modules/egg-onerror/app.js", "start": 1755792610965, "end": 1755792610969, "duration": 4, "pid": 18872, "index": 52}, {"name": "Require(43) node_modules/egg-i18n/app.js", "start": 1755792610970, "end": 1755792610974, "duration": 4, "pid": 18872, "index": 53}, {"name": "Require(44) node_modules/egg-watcher/app.js", "start": 1755792610974, "end": 1755792610978, "duration": 4, "pid": 18872, "index": 54}, {"name": "Require(45) node_modules/egg-schedule/app.js", "start": 1755792610978, "end": 1755792610979, "duration": 1, "pid": 18872, "index": 55}, {"name": "Require(46) node_modules/egg-multipart/app.js", "start": 1755792610979, "end": 1755792610979, "duration": 0, "pid": 18872, "index": 56}, {"name": "Require(47) node_modules/egg-development/app.js", "start": 1755792610980, "end": 1755792610980, "duration": 0, "pid": 18872, "index": 57}, {"name": "Require(48) node_modules/egg-logrotator/app.js", "start": 1755792610980, "end": 1755792610980, "duration": 0, "pid": 18872, "index": 58}, {"name": "Require(49) node_modules/egg-static/app.js", "start": 1755792610980, "end": 1755792610980, "duration": 0, "pid": 18872, "index": 59}, {"name": "Require(50) node_modules/egg-sequelize/app.js", "start": 1755792610981, "end": 1755792610981, "duration": 0, "pid": 18872, "index": 60}, {"name": "Require(51) node_modules/egg-cors/app.js", "start": 1755792610981, "end": 1755792610981, "duration": 0, "pid": 18872, "index": 61}, {"name": "Require(52) node_modules/egg-view-nunjucks/app.js", "start": 1755792610981, "end": 1755792610981, "duration": 0, "pid": 18872, "index": 62}, {"name": "Require(53) app.js", "start": 1755792610982, "end": 1755792610982, "duration": 0, "pid": 18872, "index": 63}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1755792610987, "end": 1755792611286, "duration": 299, "pid": 18872, "index": 64}, {"name": "Load \"Symbol(model)\" to Application", "start": 1755792611194, "end": 1755792611204, "duration": 10, "pid": 18872, "index": 65}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1755792611205, "end": 1755792611293, "duration": 88, "pid": 18872, "index": 66}, {"name": "Did Load in app.js:didLoad", "start": 1755792611206, "end": 1755792611271, "duration": 65, "pid": 18872, "index": 67}, {"name": "Load Service", "start": 1755792611206, "end": 1755792611210, "duration": 4, "pid": 18872, "index": 68}, {"name": "Load \"service\" to Context", "start": 1755792611206, "end": 1755792611210, "duration": 4, "pid": 18872, "index": 69}, {"name": "Load Middleware", "start": 1755792611210, "end": 1755792611254, "duration": 44, "pid": 18872, "index": 70}, {"name": "Load \"middlewares\" to Application", "start": 1755792611210, "end": 1755792611249, "duration": 39, "pid": 18872, "index": 71}, {"name": "Load Controller", "start": 1755792611254, "end": 1755792611257, "duration": 3, "pid": 18872, "index": 72}, {"name": "Load \"controller\" to Application", "start": 1755792611254, "end": 1755792611257, "duration": 3, "pid": 18872, "index": 73}, {"name": "Load Router", "start": 1755792611258, "end": 1755792611259, "duration": 1, "pid": 18872, "index": 74}, {"name": "Require(54) app/router.js", "start": 1755792611258, "end": 1755792611258, "duration": 0, "pid": 18872, "index": 75}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1755792611258, "end": 1755792611271, "duration": 13, "pid": 18872, "index": 76}, {"name": "Will Ready in app.js:will<PERSON><PERSON>y", "start": 1755792611293, "end": 1755792611293, "duration": 0, "pid": 18872, "index": 77}]
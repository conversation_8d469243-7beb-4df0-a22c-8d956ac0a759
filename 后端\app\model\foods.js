"use strict";

module.exports = (app) => {
  const { STRING, INTEGER } = app.Sequelize;

  const Location = app.model.define(
    "foods",
    {
      id: { type: INTEGER, primaryKey: true, autoIncrement: true },
      title: STRING(255),
      lid: INTEGER,
      pid: INTEGER,
      img: STRING(255),
      time: STRING(30),
    },
    {
      tableName: "foods", // 指定表名称
      timestamps: false, // 不自动增加创建时间和更新时间
    }
  );

  // 添加分页方法
  Location.paginate = async function (
    page = 1,
    pageSize = 10,
    where = {},
    order = [["id", "desc"]]
  ) {
    const offset = (page - 1) * pageSize;

    const result = await this.findAndCountAll({
      where,
      offset,
      limit: pageSize,
      order,
    });

    return {
      total: result.count,
      data: result.rows,
      currentPage: page,
      pageSize,
    };
  };

  return Location;
};
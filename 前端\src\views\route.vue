<template>
  <div class="page">
    <div class="map h-[100vh] w-[100vw]" id="map-route"></div>
    <div class="fixed top-5 z-50 w-full flex justify-center">
      <div class="bg-[#fff] flex flex-col items-center justify-center p-3 rounded-2xl card" v-if="distance">
        <div class="text mb-4 w-[90%]">总里程: <span class="pl-5">{{ distance }}</span> </div>
        <div class="text mb-4 w-[90%]">总时长: <span class="pl-5">{{ times }}</span> </div>
        <div class="text w-[90%]">目标点: <span class="pl-5">{{ paths_data.jsons.length }}</span> 个</div>
        <div class="font-[40px] w-[90%] pt-2">
          {{ paths_str }}
        </div>
      </div>
    </div>

    <div class="fixed bottom-10 z-50 flex justify-center w-full bottom-type">
      <van-tabs v-model:active="type" type="card">
        <van-tab title="路线"></van-tab>
        <van-tab title="美食"></van-tab>
        <van-tab title="住宿"></van-tab>
      </van-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onBeforeUnmount, ref } from "vue";
import { useRouter } from 'vue-router'
import { pathslist } from "@/api/paths";
import { showDialog } from "vant";
import { foodslist } from "@/api/foods";
import { hotelslist } from "@/api/hotels";

const router = useRouter()
const code = computed(() => {
  return router.currentRoute.value.params.code
})
//是否可以返回上一页
const goBack = computed(() => {
  return window.history.length > 1
})

const type = ref<string>('路线')

// dom加载完成
onMounted(async () => {
  await getData()
  await getFoods()
  await getHotels()
});

// 查询路径信息
const paths_data = ref<any>(null)
const paths_str = ref('')
const pid = ref<number | null>(null)
const getData = async () => {
  try {
    if (!code.value) {
      throw new Error("路径编号不能为空!");
    }
    const response = await pathslist({
      code: code.value,
      offset: 1,
      limit: 1
    });
    if (response.success && response.data.data.length) {
      paths_data.value = {
        ...response.data.data[0],
        jsons: JSON.parse(response.data.data[0].jsons)
      }
      pid.value = paths_data.value.id
      paths_str.value = paths_data.value.jsons.map((i: any) => {
        return i.name
      }).join('→')
      init()
    } else {
      throw new Error("没有获取到数据!");
    }
  } catch (error: any) {
    showDialog({
      message: error.message
    })
    console.log(error);
  }
}

// 查询美食信息
const foods_data = ref<any>(null)
const getFoods = async () => {
  const response = await foodslist({
    pid: pid.value,
    offset: 1,
    limit: 999
  });
  if (response.success) {
    foods_data.value = response.data.data
  }
}

// 查询住宿信息
const hotels_data = ref<any>(null)
const getHotels = async () => {
  const response = await hotelslist({
    pid: pid.value,
    offset: 1,
    limit: 999
  });
  if (response.success) {
    hotels_data.value = response.data.data
  }
}
//  初始化
let map: any = null
const init = () => {
  map = new BMapGL.Map("map-route");
  map.enableScrollWheelZoom();     //开启鼠标滚轮缩放

  map.centerAndZoom({ lng: paths_data.value.jsons[0].lng, lat: paths_data.value.jsons[0].lat }, 10);
  let scaleCtrl = new BMapGL.ScaleControl();  // 添加比例尺控件
  map.addControl(scaleCtrl);
  let zoomCtrl = new BMapGL.ZoomControl();  // 添加缩放控件
  map.addControl(zoomCtrl);

  planFun()
  // trackFun()
}


//路径规划
let driving: any = null
const times = ref<string>('')
const distance = ref<string>('')
const planFun = () => {
  driving = new BMapGL.DrivingRouteLine(map, {
    renderOptions: {
      map: map,
      policy: "BMAP_DRIVING_POLICY_FIRST_HIGHWAYS"
    },
    onSearchComplete: function (results: any) {
      let plan = results.getPlan(0);
      times.value = plan.getDuration(true) //获取时间
      distance.value = plan.getDistance(true) //获取时间
    }
  });
  let start: any = null
  let end: any = null
  // 获取中间的途经点
  let passBy = []
  for (let i = 0; i < paths_data.value.jsons.length; i++) {
    if (i === 0) {
      start = new BMapGL.Point(paths_data.value.jsons[i].lng, paths_data.value.jsons[i].lat)
    } else if (i === paths_data.value.jsons.length - 1) {
      end = new BMapGL.Point(paths_data.value.jsons[i].lng, paths_data.value.jsons[i].lat)
    } else {
      passBy.push(`${paths_data.value.jsons[i].lat},${paths_data.value.jsons[i].lng}`)
    }
  }
  displayMarkers(paths_data.value.jsons)

  driving.search(start, end, { waypoints: passBy.join('|') });

}


// 设置markers
const displayMarkers = (markers: any[]) => {
  // 清除现有标点
  map.clearOverlays()

  // 添加所有标点
  markers.forEach((marker: any, index) => {
    const point = new BMapGL.Point(marker.lng, marker.lat)
    const mapMarker = new BMapGL.Marker(point)


    // 添加标签
    if (marker.name) {
      const label = new BMapGL.Label(`${index + 1}、${marker.name}`, {
        offset: new BMapGL.Size(10, -50)
      })
      label.setStyle({
        color: '#fff',
        fontSize: '16px',
        backgroundColor: '#ffa61b',
        border: '1px solid #ffa61b',
        borderRadius: '3px',
        padding: '2px 5px'
      })
      mapMarker.setLabel(label)
    }

    map.addOverlay(mapMarker)
  })
}

//轨迹动画
let trackAni: any = null
const trackFun = (list: any[]) => {

  // let point = []
  // for (let i = 0; i < list.length; i++) {
  //   point.push(new BMapGL.Point(list[i].lng, list[i].lat))
  // }
  let pl = new BMapGL.Polyline(list);

  trackAni = new BMapGLLib.TrackAnimation(map, pl, {
    overallView: true, // 动画完成后自动调整视野到总览
    tilt: 30,          // 轨迹播放的角度，默认为55
    duration: 20000,   // 动画持续时长，默认为10000，单位ms
    delay: 3000        // 动画开始的延迟，默认0，单位ms
  });
  trackAni.start();
}

// 停止规划
const stopRoute = () => {
  console.log(trackAni);

  trackAni.cancel();
}


//页面卸载
onBeforeUnmount(() => {
  if (map) {
    map.destroy();
    map = null;
  }
  if (trackAni) {
    trackAni.cancel();
  }
});

</script>

<style scoped lang="scss">
@media (min-width: 769px) {
  .card {
    width: 600px
  }

  .text {
    font-size: 25px;
  }

  .bottom-type {
    ::v-deep .van-tabs__nav {
      height: 50px;
    }

    ::v-deep .van-tabs__wrap {
      height: 50px
    }

    ::v-deep .van-tab {
      width: 100px;

      span {
        font-size: 16px;
      }
    }
  }

}

@media (max-width: 480px) {
  .card {
    width: 1800px
  }

  .text {
    font-size: 100px;
  }
}
</style>
